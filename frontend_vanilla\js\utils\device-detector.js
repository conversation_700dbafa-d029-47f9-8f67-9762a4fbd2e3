/**
 * 設備和環境檢測工具 - 原生JavaScript版本
 * 用於識別運行環境並提供相應的功能支持檢測
 */

/**
 * 檢測是否可以使用DOM
 */
function canUseDom() {
  return !!(typeof window !== 'undefined' && window.document && window.document.createElement);
}

/**
 * 檢測是否為移動設備
 */
function isMobileDevice() {
  if (!canUseDom()) return false;
  
  const userAgent = navigator.userAgent || navigator.vendor || window.opera;
  
  // 檢測移動設備的用戶代理字符串
  const mobileRegex = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i;
  
  // 檢測觸摸支持
  const hasTouchSupport = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  
  // 檢測屏幕尺寸（移動設備通常寬度較小）
  const isSmallScreen = window.innerWidth <= 768;
  
  return mobileRegex.test(userAgent) || (hasTouchSupport && isSmallScreen);
}

/**
 * 檢測是否為Android設備
 */
function isAndroid() {
  if (!canUseDom()) return false;
  return /android/i.test(navigator.userAgent);
}

/**
 * 檢測是否為iOS設備
 */
function isIOS() {
  if (!canUseDom()) return false;
  return /ios|iphone|ipad|ipod/i.test(navigator.userAgent);
}

/**
 * 檢測是否為Web瀏覽器環境
 */
function isWebBrowser() {
  return canUseDom() && !isMobileDevice();
}

/**
 * 檢測是否為平板設備
 */
function isTablet() {
  if (!canUseDom()) return false;
  
  const userAgent = navigator.userAgent;
  const isTabletUA = /ipad|android(?!.*mobile)|tablet/i.test(userAgent);
  const isLargeScreen = window.innerWidth >= 768 && window.innerWidth <= 1024;
  
  return isTabletUA || (isMobileDevice() && isLargeScreen);
}

/**
 * 獲取設備類型
 */
function getDeviceType() {
  if (isTablet()) return 'tablet';
  if (isMobileDevice()) return 'mobile';
  return 'desktop';
}

/**
 * 檢測相機功能支持
 */
async function getCameraCapabilities() {
  const capabilities = {
    hasCamera: false,
    hasUserMedia: false,
    supportsFacingMode: false,
    supportsConstraints: false,
    availableCameras: []
  };

  if (!canUseDom() || !navigator.mediaDevices) {
    return capabilities;
  }

  try {
    // 檢測 getUserMedia 支持
    capabilities.hasUserMedia = !!navigator.mediaDevices.getUserMedia;

    // 檢測可用的相機設備
    if (navigator.mediaDevices.enumerateDevices) {
      const devices = await navigator.mediaDevices.enumerateDevices();
      capabilities.availableCameras = devices.filter(device => device.kind === 'videoinput');
      capabilities.hasCamera = capabilities.availableCameras.length > 0;

      // 檢測是否支持 facingMode 約束
      if (capabilities.hasCamera) {
        try {
          const stream = await navigator.mediaDevices.getUserMedia({
            video: { facingMode: 'environment' }
          });
          capabilities.supportsFacingMode = true;
          capabilities.supportsConstraints = true;
          
          // 立即停止測試流
          stream.getTracks().forEach(track => track.stop());
        } catch (error) {
          // 如果 facingMode 不支持，嘗試基本的視頻約束
          try {
            const stream = await navigator.mediaDevices.getUserMedia({ video: true });
            capabilities.supportsConstraints = true;
            stream.getTracks().forEach(track => track.stop());
          } catch (basicError) {
            console.warn('基本相機功能不可用:', basicError);
          }
        }
      }
    }
  } catch (error) {
    console.warn('檢測相機功能時出錯:', error);
  }

  return capabilities;
}

/**
 * 檢測是否支持全屏
 */
function supportsFullscreen() {
  if (!canUseDom()) return false;
  
  const element = document.documentElement;
  return !!(
    element.requestFullscreen ||
    element.mozRequestFullScreen ||
    element.webkitRequestFullscreen ||
    element.msRequestFullscreen
  );
}

/**
 * 檢測是否支持屏幕方向
 */
function supportsOrientation() {
  if (!canUseDom()) return false;
  return 'orientation' in window || 'screen' in window && 'orientation' in window.screen;
}

/**
 * 獲取設備方向
 */
function getDeviceOrientation() {
  if (!canUseDom()) return 'unknown';
  
  if (window.screen && window.screen.orientation) {
    return window.screen.orientation.type;
  }
  
  if (window.orientation !== undefined) {
    const orientation = Math.abs(window.orientation);
    return orientation === 90 ? 'landscape' : 'portrait';
  }
  
  // 根據窗口尺寸判斷
  return window.innerWidth > window.innerHeight ? 'landscape' : 'portrait';
}

/**
 * 獲取完整的環境信息
 */
async function getEnvironmentInfo() {
  const info = {
    // 設備信息
    isMobile: isMobileDevice(),
    isTablet: isTablet(),
    isDesktop: !isMobileDevice() && !isTablet(),
    isAndroid: isAndroid(),
    isIOS: isIOS(),
    deviceType: getDeviceType(),
    
    // 瀏覽器信息
    userAgent: navigator.userAgent,
    
    // 屏幕信息
    screenWidth: window.innerWidth,
    screenHeight: window.innerHeight,
    devicePixelRatio: window.devicePixelRatio || 1,
    orientation: getDeviceOrientation(),
    
    // 功能支持
    supportsFullscreen: supportsFullscreen(),
    supportsOrientation: supportsOrientation(),
    hasTouchSupport: 'ontouchstart' in window || navigator.maxTouchPoints > 0,
    
    // 相機功能
    camera: await getCameraCapabilities()
  };
  
  return info;
}

/**
 * 推薦的拍照模式
 */
async function getRecommendedCameraMode() {
  const envInfo = await getEnvironmentInfo();
  
  if (!envInfo.camera.hasCamera) {
    return 'none'; // 無相機支持
  }
  
  if (envInfo.isMobile || envInfo.isTablet) {
    return 'mobile'; // 移動端全屏模式
  }
  
  return 'web'; // Web端模態框模式
}

// 導出到全局
window.DeviceDetector = {
  canUseDom,
  isMobileDevice,
  isAndroid,
  isIOS,
  isWebBrowser,
  isTablet,
  getDeviceType,
  getCameraCapabilities,
  supportsFullscreen,
  supportsOrientation,
  getDeviceOrientation,
  getEnvironmentInfo,
  getRecommendedCameraMode
};

// 為了兼容性，也導出單個函數
window.canUseDom = canUseDom;
window.isMobileDevice = isMobileDevice;
window.isAndroid = isAndroid;
window.isIOS = isIOS;
window.isWebBrowser = isWebBrowser;
window.isTablet = isTablet;
window.getDeviceType = getDeviceType;
window.getCameraCapabilities = getCameraCapabilities;
window.supportsFullscreen = supportsFullscreen;
window.supportsOrientation = supportsOrientation;
window.getDeviceOrientation = getDeviceOrientation;
window.getEnvironmentInfo = getEnvironmentInfo;
window.getRecommendedCameraMode = getRecommendedCameraMode;

from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from backend.api.v1 import card, ocr
from backend.core.config import settings
import os

app = FastAPI(title="名片OCR API", description="名片掃描與管理後端", version="1.0.0")

# CORS設置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 路由註冊
app.include_router(card.router, prefix="/api/v1/cards", tags=["名片管理"])
app.include_router(ocr.router, prefix="/api/v1/ocr", tags=["OCR"])

# 靜態文件服務配置
app.mount("/css", StaticFiles(directory="frontend/css"), name="css")
app.mount("/js", StaticFiles(directory="frontend/js"), name="js")

# 可選的icons目錄
if os.path.exists("frontend/icons"):
    app.mount("/icons", StaticFiles(directory="frontend/icons"), name="icons")

# 服務PWA相關文件
@app.get("/manifest.json")
async def get_manifest():
    return FileResponse("frontend/manifest.json", media_type="application/json")

@app.get("/sw.js")
async def get_service_worker():
    return FileResponse("frontend/sw.js", media_type="application/javascript")

# SPA路由支持 - 所有非API路徑都返回index.html
@app.get("/{full_path:path}")
async def serve_spa(full_path: str):
    # API路徑不處理
    if full_path.startswith("api/"):
        return {"error": "API endpoint not found"}

    # 檢查是否為靜態文件請求
    if "." in full_path and not full_path.endswith(".html"):
        # 嘗試從frontend目錄提供文件
        file_path = f"frontend/{full_path}"
        if os.path.exists(file_path):
            return FileResponse(file_path)
        else:
            return {"error": "File not found"}

    # 所有其他路徑返回index.html（SPA路由）
    return FileResponse("frontend/index.html", media_type="text/html")

# 啟動事件
@app.on_event("startup")
def startup_event():
    from backend.models.db import Base, engine
    # 確保資料庫表存在
    Base.metadata.create_all(bind=engine)
    print("🚀 名片OCR後端啟動完成！") 
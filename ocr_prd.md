名片OCR分類\n\n掃描名片 or 上傳名片圖片(另一個快速處理備案)\nOCR識別\n資料確認與編輯\n名片資料管理\n資料匯出\n\n\n1. 啟動掃描工具\n步驟：\n\n\n用戶通過瀏覽器打開名片掃描網頁。\n\n\n首頁顯示掃描工具的介紹或操作提示，並提供「開始掃描」按鈕。\n\n\n系統檢查設備的相機權限，若無授權，會彈出請求權限的提示。\n\n\n預期結果：啟動掃描界面，準備掃描名片。\n\n\n2. 掃描名片\n步驟：\n\n\n用戶選擇「掃描名片」進入相機介面。\n\n\n相機介面顯示一個名片輪廓框，用戶將名片對準框內。\n\n\n系統自動對焦，並且會檢查名片是否在合適的範圍內，若名片位置不正確，會顯示提示訊息（如「請調整名片至框內」）。\n\n\n用戶點擊「拍攝」按鈕，完成名片掃描。\n\n\n預期結果：掃描的名片正面圖片會自動捕捉並顯示預覽。\n\n\n3. 掃描反面（如果有）\n步驟：\n\n\n掃描正面完成後，系統提示用戶是否掃描反面。\n\n\n用戶可選擇「掃描反面」，進入相機界面繼續掃描反面，或選擇「跳過」只保存正面資料。\n\n\n預期結果：反面掃描完成後，正面和反面資料都會儲存為一筆完整的名片。\n\n\n4. OCR識別\n步驟：\n\n\n掃描完成後，系統自動開始OCR識別，並將名片上的文字資料提取出來。\n\n\n用戶會看到OCR處理的進度條，並在處理完成後顯示識別結果（包括姓名、職位、公司、電話、電子郵件等）。\n\n\n預期結果：名片上的資料自動轉換為結構化格式，並顯示在資料確認頁面。\n\n\n5. 資料確認與編輯\n步驟：\n\n\n用戶可檢視OCR識別後的資料，系統自動將識別結果填入相應的欄位。\n\n\n用戶可以手動編輯資料，修改錯誤或補充缺失的資訊。\n\n\n完成編輯後，用戶點擊「儲存」，圖黨及讀取到的欄位紀錄為一筆。\n紀錄不為excel格式，因為要可以搭配圖檔看，但匯出會是excel形式\n\n\n預期結果：用戶確認無誤後，名片資料被儲存到系統內。\n\n\n6. 上傳名片圖片 (其他支援用例)\n步驟：\n\n\n若用戶不想直接掃描名片，也可以選擇「上傳圖片」功能。\n\n\n用戶選擇本地圖片或拖放圖片進行上傳，支援JPG、PNG等常見格式。\n\n\n系統自動進行圖像處理，裁剪、旋轉、去噪等處理後進行OCR識別。\n\n\n用戶可進行資料確認和編輯。\n\n\n預期結果：上傳的名片圖片經過處理後，與掃描功能一樣完成OCR識別並進行編輯。\n\n\n7. 名片資料管理\n步驟：\n\n\n所有掃描過或上傳過的名片資料會集中顯示在名片管理頁面。\n\n\n用戶可以通過搜尋框、篩選功能或排序功能查找特定名片。\n\n\n用戶也可以對名片進行分類，選擇「公司名稱」、「職位」、「聯繫人」等進行分類管理。\n\n\n預期結果：用戶能輕鬆管理名片資料，並進行資料搜尋和分類。\n\n\n8. 資料匯出\n步驟：\n\n\n在名片管理頁面，用戶可以選擇「匯出」功能。\n\n\n系統提供不同的匯出選項（CSV、Excel、vCard）。\n\n\n用戶可以選擇單筆名片或批量匯出。\n\n\n點擊「匯出」後，系統會生成匯出文件並提供下載鏈接。\n\n\n預期結果：用戶能將所選名片資料匯出，並能夠在其他系統（如CRM、電子郵件）中使用這些資料。
/**
 * Service Worker - PWA支持
 */

const CACHE_NAME = 'card-ocr-app-v1.0.0';
const urlsToCache = [
  '/',
  '/index.html',
  '/css/main.css',
  '/css/components.css',
  '/css/mobile-camera.css',
  '/js/app.js',
  '/js/router.js',
  '/js/components/ui-components.js',
  '/js/components/mobile-camera.js',
  '/js/utils/device-detector.js',
  '/js/utils/camera-strategies.js',
  '/js/utils/camera-manager.js',
  '/js/utils/api.js',
  '/js/pages/home.js',
  '/js/pages/scan-upload.js',
  '/js/pages/card-manager.js',
  '/js/pages/add-card.js',
  '/js/pages/card-detail.js',
  '/js/pages/camera-test.js',
  'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
  'https://cdnjs.cloudflare.com/ajax/libs/axios/1.6.2/axios.min.js'
];

// 安裝事件
self.addEventListener('install', (event) => {
  console.log('Service Worker 安裝中...');
  
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('緩存已打開');
        return cache.addAll(urlsToCache);
      })
      .then(() => {
        console.log('所有資源已緩存');
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error('緩存資源失敗:', error);
      })
  );
});

// 激活事件
self.addEventListener('activate', (event) => {
  console.log('Service Worker 激活中...');
  
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            console.log('刪除舊緩存:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    }).then(() => {
      console.log('Service Worker 激活完成');
      return self.clients.claim();
    })
  );
});

// 請求攔截
self.addEventListener('fetch', (event) => {
  const request = event.request;
  const url = new URL(request.url);
  
  // 只處理GET請求
  if (request.method !== 'GET') {
    return;
  }
  
  // API請求使用網絡優先策略
  if (url.pathname.startsWith('/api/')) {
    event.respondWith(
      fetch(request)
        .then((response) => {
          // 如果網絡請求成功，返回響應
          if (response.ok) {
            return response;
          }
          throw new Error('Network response was not ok');
        })
        .catch(() => {
          // 網絡失敗時返回離線提示
          return new Response(
            JSON.stringify({
              error: '網絡連接不可用，請檢查網絡設置'
            }),
            {
              status: 503,
              statusText: 'Service Unavailable',
              headers: {
                'Content-Type': 'application/json'
              }
            }
          );
        })
    );
    return;
  }
  
  // 靜態資源使用緩存優先策略
  event.respondWith(
    caches.match(request)
      .then((response) => {
        // 如果緩存中有資源，直接返回
        if (response) {
          return response;
        }
        
        // 否則從網絡獲取
        return fetch(request).then((response) => {
          // 檢查響應是否有效
          if (!response || response.status !== 200 || response.type !== 'basic') {
            return response;
          }
          
          // 克隆響應用於緩存
          const responseToCache = response.clone();
          
          caches.open(CACHE_NAME)
            .then((cache) => {
              cache.put(request, responseToCache);
            });
          
          return response;
        });
      })
      .catch(() => {
        // 如果是HTML請求且緩存和網絡都失敗，返回離線頁面
        if (request.headers.get('accept').includes('text/html')) {
          return caches.match('/index.html');
        }
      })
  );
});

// 後台同步
self.addEventListener('sync', (event) => {
  console.log('後台同步事件:', event.tag);
  
  if (event.tag === 'background-sync') {
    event.waitUntil(
      // 這裡可以添加後台同步邏輯
      console.log('執行後台同步任務')
    );
  }
});

// 推送通知
self.addEventListener('push', (event) => {
  console.log('收到推送消息:', event);
  
  const options = {
    body: event.data ? event.data.text() : '您有新的消息',
    icon: '/icon-192x192.png',
    badge: '/icon-72x72.png',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    },
    actions: [
      {
        action: 'explore',
        title: '查看詳情',
        icon: '/icon-192x192.png'
      },
      {
        action: 'close',
        title: '關閉',
        icon: '/icon-192x192.png'
      }
    ]
  };
  
  event.waitUntil(
    self.registration.showNotification('名片OCR應用', options)
  );
});

// 通知點擊
self.addEventListener('notificationclick', (event) => {
  console.log('通知被點擊:', event);
  
  event.notification.close();
  
  if (event.action === 'explore') {
    // 打開應用
    event.waitUntil(
      clients.openWindow('/')
    );
  } else if (event.action === 'close') {
    // 關閉通知
    event.notification.close();
  } else {
    // 默認行為：打開應用
    event.waitUntil(
      clients.openWindow('/')
    );
  }
});

// 消息處理
self.addEventListener('message', (event) => {
  console.log('收到消息:', event.data);
  
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'GET_VERSION') {
    event.ports[0].postMessage({
      version: CACHE_NAME
    });
  }
});

// 錯誤處理
self.addEventListener('error', (event) => {
  console.error('Service Worker 錯誤:', event.error);
});

self.addEventListener('unhandledrejection', (event) => {
  console.error('Service Worker 未處理的Promise錯誤:', event.reason);
});

console.log('Service Worker 腳本已載入');

<!DOCTYPE html>
<html lang="zh-Hant">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>名片掃描/上傳</title>
  <style>
    body {
      font-family: 'Noto Sans TC', 'Microsoft JhengHei', <PERSON>l, sans-serif;
      background: #f5f5f5;
      margin: 0;
      padding: 0;
      color: #222;
    }
    .scan-upload-page {
      max-width: 480px;
      margin: 0 auto;
      background: #fff;
      min-height: 100vh;
      box-shadow: 0 2px 8px rgba(0,0,0,0.04);
    }
    .navbar {
      background: #1677ff;
      color: #fff;
      padding: 16px;
      font-size: 20px;
      font-weight: bold;
      text-align: center;
      position: sticky;
      top: 0;
      z-index: 10;
    }
    .content {
      padding: 16px;
    }
    .card {
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.06);
      margin-bottom: 16px;
      padding: 16px;
    }
    .card-title {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 12px;
      display: flex;
      align-items: center;
      gap: 8px;
    }
    .divider {
      border: none;
      border-top: 1px solid #eee;
      margin: 18px 0 12px 0;
    }
    .image-capture-section {
      margin-bottom: 12px;
    }
    .capture-mode-switch {
      display: flex;
      gap: 8px;
      margin-bottom: 12px;
    }
    .capture-mode-switch button {
      flex: 1;
      padding: 8px 0;
      border-radius: 6px;
      border: 1px solid #1677ff;
      background: #fff;
      color: #1677ff;
      font-weight: bold;
      cursor: pointer;
      transition: background 0.2s, color 0.2s;
    }
    .capture-mode-switch button.active, .capture-mode-switch button:active {
      background: #1677ff;
      color: #fff;
    }
    .single-capture-frame {
      margin-bottom: 12px;
    }
    .current-side-title {
      font-size: 15px;
      font-weight: bold;
      text-align: center;
      margin-bottom: 8px;
    }
    .preview-img {
      width: 100%;
      height: clamp(220px, 40vw, 400px);
      object-fit: cover;
      border-radius: 8px;
      margin-bottom: 16px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      transition: all 0.3s;
    }
    .empty-capture {
      width: 100%;
      height: clamp(220px, 40vw, 400px);
      border: 2px dashed #d9d9d9;
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #999;
      margin-bottom: 16px;
      background: #fafafa;
      transition: all 0.3s;
    }
    .capture-btns {
      display: flex;
      gap: 8px;
      justify-content: center;
      margin-bottom: 8px;
    }
    .capture-btns button {
      flex: 1;
      padding: 10px 0;
      border-radius: 6px;
      border: none;
      font-size: 16px;
      font-weight: bold;
      cursor: pointer;
      background: #1677ff;
      color: #fff;
      transition: background 0.2s;
    }
    .capture-btns button.secondary {
      background: #fff;
      color: #1677ff;
      border: 1px solid #1677ff;
    }
    .capture-status {
      margin-top: 8px;
      text-align: center;
      font-size: 13px;
      color: #888;
    }
    .form-section {
      margin-bottom: 8px;
    }
    .form-label {
      font-size: 14px;
      margin-bottom: 4px;
      font-weight: 500;
    }
    .form-input, .form-textarea {
      width: 100%;
      padding: 8px;
      border: 1px solid #d9d9d9;
      border-radius: 5px;
      font-size: 15px;
      margin-bottom: 10px;
      box-sizing: border-box;
      background: #fafcff;
    }
    .form-textarea {
      min-height: 60px;
      resize: vertical;
    }
    .save-btn {
      width: 100%;
      padding: 14px 0;
      font-size: 18px;
      font-weight: bold;
      background: #52c41a;
      color: #fff;
      border: none;
      border-radius: 8px;
      margin-top: 12px;
      cursor: pointer;
      transition: background 0.2s;
    }
    .save-btn:disabled {
      background: #b7eb8f;
      color: #fff;
      cursor: not-allowed;
    }
    /* Toast/Loading/Modal */
    .toast {
      position: fixed;
      left: 50%;
      top: 40%;
      transform: translate(-50%, -50%);
      background: rgba(34,34,34,0.95);
      color: #fff;
      padding: 16px 28px;
      border-radius: 8px;
      font-size: 16px;
      z-index: 9999;
      box-shadow: 0 2px 8px rgba(0,0,0,0.18);
      animation: fadeIn 0.2s;
    }
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    .loading {
      position: fixed;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      background: rgba(255,255,255,0.9);
      color: #1677ff;
      padding: 18px 32px;
      border-radius: 8px;
      font-size: 18px;
      z-index: 9999;
      display: flex;
      align-items: center;
      gap: 10px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.12);
    }
    .modal-mask {
      position: fixed;
      left: 0; top: 0; right: 0; bottom: 0;
      background: rgba(0,0,0,0.35);
      z-index: 9998;
    }
    .modal {
      position: fixed;
      left: 50%; top: 50%;
      transform: translate(-50%, -50%);
      background: #fff;
      border-radius: 10px;
      padding: 24px 18px 18px 18px;
      min-width: 260px;
      z-index: 9999;
      box-shadow: 0 2px 16px rgba(0,0,0,0.18);
      text-align: center;
    }
    .modal .modal-title {
      font-size: 17px;
      font-weight: bold;
      margin-bottom: 12px;
    }
    .modal .modal-actions {
      margin-top: 18px;
      display: flex;
      gap: 12px;
      justify-content: center;
    }
    @media (max-width: 600px) {
      .scan-upload-page { max-width: 100vw; }
      .card { padding: 10px; }
      .content { padding: 8px; }
    }
  </style>
</head>
<body>
<div class="scan-upload-page">
  <div class="navbar">名片資料輸入</div>
  <div class="content">
    <!-- 拍照/上傳區塊 -->
    <div class="card">
      <div class="card-title">拍攝名片</div>
      <div class="image-capture-section">
        <div class="capture-mode-switch">
          <button id="btn-front" class="active">正面</button>
          <button id="btn-back">反面</button>
          <button id="btn-parse" class="secondary">解析</button>
        </div>
        <div class="single-capture-frame">
          <div class="current-side-title" id="current-side-title">當前拍攝: 正面</div>
          <img id="preview-img" class="preview-img" style="display:none;" />
          <div id="empty-capture" class="empty-capture">
            <div style="font-size:48px;">📷</div>
            <div style="font-size:16px;">請拍攝名片正面</div>
            <div style="font-size:14px;color:#bbb;margin-top:8px;">點擊下方按鈕開始拍照</div>
          </div>
          <div class="capture-btns">
            <button id="btn-camera"><span style="font-size:20px;">📷</span> 拍照</button>
            <button id="btn-gallery" class="secondary"><span style="font-size:20px;">🖼️</span> 相冊</button>
          </div>
          <div class="capture-status" id="capture-status">
            <span id="front-status-dot" style="display:inline-block;width:8px;height:8px;border-radius:50%;background:#d9d9d9;"></span>
            <span style="margin-right:12px;">正面 未拍攝</span>
            <span id="back-status-dot" style="display:inline-block;width:8px;height:8px;border-radius:50%;background:#d9d9d9;"></span>
            <span>反面 未拍攝</span>
          </div>
        </div>
      </div>
    </div>
    <!-- 名片資料表單區塊 -->
    <div class="card">
      <div class="card-title">名片資料</div>
      <form id="card-form" autocomplete="off">
        <div class="form-section">
          <hr class="divider" />
          <div class="form-label">姓名 *</div>
          <input class="form-input" id="name" name="name" placeholder="請輸入姓名" required />
          <div class="form-label">公司名稱</div>
          <input class="form-input" id="company_name" name="company_name" placeholder="請輸入公司名稱" />
          <div class="form-label">職位</div>
          <input class="form-input" id="position" name="position" placeholder="請輸入職位" />
        </div>
        <div class="form-section">
          <hr class="divider" />
          <div class="form-label">手機</div>
          <input class="form-input" id="mobile_phone" name="mobile_phone" placeholder="請輸入手機號碼" />
          <div class="form-label">公司電話</div>
          <input class="form-input" id="office_phone" name="office_phone" placeholder="請輸入公司電話" />
          <div class="form-label">Email</div>
          <input class="form-input" id="email" name="email" placeholder="請輸入Email地址" />
          <div class="form-label">Line ID</div>
          <input class="form-input" id="line_id" name="line_id" placeholder="請輸入Line ID" />
        </div>
        <div class="form-section">
          <hr class="divider" />
          <div class="form-label">公司地址一</div>
          <input class="form-input" id="company_address_1" name="company_address_1" placeholder="請輸入公司地址" />
          <div class="form-label">公司地址二</div>
          <input class="form-input" id="company_address_2" name="company_address_2" placeholder="請輸入公司地址（補充）" />
        </div>
        <div class="form-section">
          <hr class="divider" />
          <div class="form-label">備註</div>
          <textarea class="form-textarea" id="notes" name="notes" placeholder="請輸入備註資訊"></textarea>
        </div>
        <button type="submit" class="save-btn" id="save-btn">保存名片</button>
      </form>
    </div>
    <input type="file" id="file-input" accept="image/*" style="display:none;" />
  </div>
</div>
<!-- Toast/Loading/Modal 元素 -->
<div id="toast" class="toast" style="display:none;"></div>
<div id="loading" class="loading" style="display:none;"><span>⏳</span> 請稍候...</div>
<div id="modal-mask" class="modal-mask" style="display:none;"></div>
<div id="modal" class="modal" style="display:none;">
  <div class="modal-title" id="modal-title"></div>
  <div id="modal-content"></div>
  <div class="modal-actions" id="modal-actions"></div>
</div>
<script>
// 狀態管理
let currentSide = 'front';
let frontImage = { file: null, preview: null, ocrText: '', parseStatus: null };
let backImage = { file: null, preview: null, ocrText: '', parseStatus: null };
let cardData = {
  name: '', company_name: '', position: '', mobile_phone: '', office_phone: '',
  email: '', line_id: '', notes: '', company_address_1: '', company_address_2: ''
};

// Toast/Loading/Modal 工具
function showToast(msg, ms=1800) {
  const toast = document.getElementById('toast');
  toast.textContent = msg;
  toast.style.display = 'block';
  setTimeout(() => { toast.style.display = 'none'; }, ms);
}
function showLoading(show) {
  document.getElementById('loading').style.display = show ? 'flex' : 'none';
}
function showModal(title, content, actionsHtml) {
  document.getElementById('modal-title').textContent = title;
  document.getElementById('modal-content').innerHTML = content;
  document.getElementById('modal-actions').innerHTML = actionsHtml;
  document.getElementById('modal-mask').style.display = 'block';
  document.getElementById('modal').style.display = 'block';
}
function closeModal() {
  document.getElementById('modal-mask').style.display = 'none';
  document.getElementById('modal').style.display = 'none';
}

// 切換正反面
function updateSideUI() {
  document.getElementById('btn-front').classList.toggle('active', currentSide==='front');
  document.getElementById('btn-back').classList.toggle('active', currentSide==='back');
  document.getElementById('current-side-title').textContent = '當前拍攝: ' + (currentSide==='front'?'正面':'反面');
  // 預覽圖
  const img = document.getElementById('preview-img');
  const empty = document.getElementById('empty-capture');
  const imgData = currentSide==='front' ? frontImage : backImage;
  if(imgData.preview) {
    img.src = imgData.preview;
    img.style.display = '';
    empty.style.display = 'none';
  } else {
    img.style.display = 'none';
    empty.style.display = '';
    empty.querySelector('div:nth-child(2)').textContent = '請拍攝名片' + (currentSide==='front'?'正面':'反面');
  }
  // 狀態點
  document.getElementById('front-status-dot').style.background = frontImage.preview ? '#52c41a' : '#d9d9d9';
  document.getElementById('back-status-dot').style.background = backImage.preview ? '#52c41a' : '#d9d9d9';
  document.getElementById('capture-status').children[1].textContent = '正面 ' + (frontImage.preview?'已拍攝':'未拍攝');
  document.getElementById('capture-status').children[3].textContent = '反面 ' + (backImage.preview?'已拍攝':'未拍攝');
}

// 切換按鈕
['btn-front','btn-back'].forEach(id=>{
  document.getElementById(id).onclick = function(){
    currentSide = (id==='btn-front')?'front':'back';
    updateSideUI();
  };
});

// 拍照（調用瀏覽器相機）
document.getElementById('btn-camera').onclick = function(){
  if(!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia){
    showToast('瀏覽器不支援相機'); return;
  }
  // 啟動相機
  const video = document.createElement('video');
  video.autoplay = true;
  video.playsInline = true;
  video.style.width = '100%';
  video.style.height = 'clamp(220px, 40vw, 400px)';
  showModal('拍照', video.outerHTML + '<div style="margin-top:16px;"><button id="take-photo-btn" style="padding:8px 24px;font-size:16px;background:#1677ff;color:#fff;border:none;border-radius:6px;">拍照</button> <button id="cancel-photo-btn" style="padding:8px 24px;font-size:16px;background:#eee;color:#222;border:none;border-radius:6px;">取消</button></div>', '');
  const modal = document.getElementById('modal');
  const v = modal.querySelector('video');
  let stream = null;
  navigator.mediaDevices.getUserMedia({video:true}).then(s=>{
    stream = s;
    v.srcObject = s;
  });
  modal.querySelector('#cancel-photo-btn').onclick = ()=>{
    if(stream) stream.getTracks().forEach(t=>t.stop());
    closeModal();
  };
  modal.querySelector('#take-photo-btn').onclick = ()=>{
    // 拍照
    const canvas = document.createElement('canvas');
    canvas.width = v.videoWidth;
    canvas.height = v.videoHeight;
    canvas.getContext('2d').drawImage(v,0,0,canvas.width,canvas.height);
    canvas.toBlob(blob=>{
      if(stream) stream.getTracks().forEach(t=>t.stop());
      closeModal();
      // 預覽
      const reader = new FileReader();
      reader.onload = function(e){
        if(currentSide==='front'){
          frontImage.file = blob;
          frontImage.preview = e.target.result;
          frontImage.parseStatus = null;
        }else{
          backImage.file = blob;
          backImage.preview = e.target.result;
          backImage.parseStatus = null;
        }
        updateSideUI();
        // 自動OCR
        performOCR(blob, currentSide);
      };
      reader.readAsDataURL(blob);
    }, 'image/jpeg');
  };
};

// 相冊選擇
const fileInput = document.getElementById('file-input');
document.getElementById('btn-gallery').onclick = function(){
  fileInput.value = '';
  fileInput.click();
};
fileInput.onchange = function(e){
  const file = e.target.files[0];
  if(!file) return;
  const reader = new FileReader();
  reader.onload = function(ev){
    if(currentSide==='front'){
      frontImage.file = file;
      frontImage.preview = ev.target.result;
      frontImage.parseStatus = null;
    }else{
      backImage.file = file;
      backImage.preview = ev.target.result;
      backImage.parseStatus = null;
    }
    updateSideUI();
    performOCR(file, currentSide);
  };
  reader.readAsDataURL(file);
};

// 解析按鈕
function updateParseBtn() {
  const imgData = currentSide==='front'?frontImage:backImage;
  document.getElementById('btn-parse').disabled = !imgData.file;
}
document.getElementById('btn-parse').onclick = function(){
  const imgData = currentSide==='front'?frontImage:backImage;
  if(!imgData.file){ showToast('請先拍攝或選擇圖片'); return; }
  if(imgData.ocrText){
    parseAndFillOCRData(imgData.ocrText, currentSide);
  }else{
    performOCR(imgData.file, currentSide);
  }
};

// OCR與智能解析
function performOCR(file, side){
  showLoading(true);
  const formData = new FormData();
  formData.append('file', file);
  fetch('/api/v1/ocr/image', {
    method: 'POST',
    body: formData
  }).then(r=>r.json()).then(res=>{
    if(res.success){
      if(side==='front') frontImage.ocrText = res.text;
      else backImage.ocrText = res.text;
      parseAndFillOCRData(res.text, side);
    }else{
      showToast('OCR識別失敗，請重試');
    }
  }).catch(()=>{
    showToast('OCR識別失敗，請檢查網絡連接');
  }).finally(()=>{
    showLoading(false);
  });
}
function parseAndFillOCRData(ocrText, side){
  showLoading(true);
  fetch('/api/v1/ocr/parse-fields', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ ocr_text: ocrText, side })
  }).then(r=>r.json()).then(res=>{
    if(res.success){
      const parsed = res.parsed_fields;
      Object.keys(parsed).forEach(k=>{
        if(parsed[k] && (!cardData[k] || cardData[k].trim()==='')){
          cardData[k] = parsed[k];
          const el = document.getElementById(k);
          if(el) el.value = parsed[k];
        }
      });
      showToast((side==='front'?'正面':'反面')+'資料解析完成！已自動填入相關欄位');
    }else{
      showToast('OCR資料解析失敗，請手動編輯');
    }
  }).catch(()=>{
    showToast('OCR資料解析失敗，請檢查網絡連接');
  }).finally(()=>{
    showLoading(false);
  });
}

// 表單同步
['name','company_name','position','mobile_phone','office_phone','email','line_id','notes','company_address_1','company_address_2'].forEach(id=>{
  document.getElementById(id).oninput = function(e){
    cardData[id] = e.target.value;
  };
});

// 保存名片
const form = document.getElementById('card-form');
form.onsubmit = function(e){
  e.preventDefault();
  if(!cardData.name.trim()){
    showToast('請輸入姓名');
    return;
  }
  showLoading(true);
  const saveData = new FormData();
  Object.keys(cardData).forEach(k=>{ if(cardData[k]) saveData.append(k, cardData[k]); });
  if(frontImage.file) saveData.append('front_image', frontImage.file);
  if(backImage.file) saveData.append('back_image', backImage.file);
  if(frontImage.ocrText) saveData.append('front_ocr_text', frontImage.ocrText);
  if(backImage.ocrText) saveData.append('back_ocr_text', backImage.ocrText);
  fetch('/api/v1/cards/', {
    method: 'POST',
    body: saveData
  }).then(r=>r.json()).then(res=>{
    if(res.success || res.id){
      showToast('名片保存成功！');
      form.reset();
      cardData = { name:'', company_name:'', position:'', mobile_phone:'', office_phone:'', email:'', line_id:'', notes:'', company_address_1:'', company_address_2:'' };
      frontImage = { file:null, preview:null, ocrText:'', parseStatus:null };
      backImage = { file:null, preview:null, ocrText:'', parseStatus:null };
      updateSideUI();
      setTimeout(()=>{ window.location.href = '/cards'; }, 1200);
    }else{
      showToast('保存失敗，請重試');
    }
  }).catch(()=>{
    showToast('保存失敗，請檢查網絡連接');
  }).finally(()=>{
    showLoading(false);
  });
};

// 初始化
updateSideUI();
updateParseBtn();
</script>
</body>
</html> 
/* 移動端全屏相機樣式 - 從原有的 MobileCameraModal.css 適配 */

.mobile-camera-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: #000;
  display: flex;
  flex-direction: column;
}

.camera-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.camera-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  background: #000;
}

/* 相機加載狀態 */
.camera-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;
  z-index: 10;
}

.camera-loading .loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
}

/* 拍照指引線 */
.camera-guides {
  position: absolute;
  top: 3%;
  left: 2%;
  right: 2%;
  bottom: 8%;
  pointer-events: none;
  z-index: 5;
  border: none;
  border-radius: 0;
}

/* 網格線輔助 */
.camera-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 4;
  opacity: 0.3;
  background-image: 
    linear-gradient(to right, rgba(255, 255, 255, 0.5) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(255, 255, 255, 0.5) 1px, transparent 1px);
  background-size: 33.33% 33.33%;
  background-repeat: repeat;
}

.camera-grid.hidden {
  display: none;
}

/* 指引角標 */
.guide-corner {
  position: absolute;
  width: 60px;
  height: 60px;
  border: 5px solid rgba(255, 255, 255, 0.95);
  border-radius: 6px;
  box-shadow:
    0 0 15px rgba(255, 255, 255, 0.4),
    inset 0 0 15px rgba(255, 255, 255, 0.15);
  animation: guidePulse 3s ease-in-out infinite;
}

.guide-corner.top-left {
  top: 0;
  left: 0;
  border-right: none;
  border-bottom: none;
}

.guide-corner.top-right {
  top: 0;
  right: 0;
  border-left: none;
  border-bottom: none;
}

.guide-corner.bottom-left {
  bottom: 0;
  left: 0;
  border-right: none;
  border-top: none;
}

.guide-corner.bottom-right {
  bottom: 0;
  right: 0;
  border-left: none;
  border-top: none;
}

@keyframes guidePulse {
  0%, 100% {
    border-color: rgba(255, 255, 255, 0.8);
    transform: scale(1);
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
  }
  50% {
    border-color: rgba(255, 255, 255, 1);
    transform: scale(1.02);
    box-shadow: 0 0 25px rgba(255, 255, 255, 0.5);
  }
}

/* 拍攝範圍提示 */
.capture-hint {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: rgba(255, 255, 255, 0.9);
  pointer-events: none;
  z-index: 6;
}

.hint-text {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 6px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.7);
  background: rgba(0, 0, 0, 0.4);
  padding: 8px 16px;
  border-radius: 20px;
  backdrop-filter: blur(6px);
}

.hint-subtext {
  font-size: 13px;
  opacity: 0.9;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.7);
  background: rgba(0, 0, 0, 0.3);
  padding: 6px 12px;
  border-radius: 16px;
  backdrop-filter: blur(4px);
}

/* 對焦指示器 */
.focus-indicator {
  position: absolute;
  width: 100px;
  height: 100px;
  border: 3px solid rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  pointer-events: none;
  z-index: 7;
  opacity: 0;
  transform: translate(-50%, -50%) scale(1.5);
  transition: all 0.3s ease;
}

.focus-indicator.active {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
  animation: focusPulse 0.6s ease-out;
}

@keyframes focusPulse {
  0% {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0;
  }
  50% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
}

/* 控制按鈕區域 */
.camera-controls {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  pointer-events: none;
  z-index: 10;
}

.controls-top {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20px;
  pointer-events: auto;
}

.controls-bottom {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  pointer-events: auto;
}

.control-button {
  width: 52px !important;
  height: 52px !important;
  border-radius: 50% !important;
  background: rgba(0, 0, 0, 0.6) !important;
  border: 2px solid rgba(255, 255, 255, 0.9) !important;
  color: white !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 22px !important;
  backdrop-filter: blur(4px) !important;
  cursor: pointer;
  transition: all 0.2s ease;
}

.control-button:active {
  background: rgba(0, 0, 0, 0.7) !important;
}

/* 拍照區域 */
.capture-area {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
}

.capture-button {
  width: 90px !important;
  height: 90px !important;
  border-radius: 50% !important;
  background: rgba(255, 255, 255, 0.95) !important;
  border: 5px solid #1677ff !important;
  color: #1677ff !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 36px !important;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4) !important;
  cursor: pointer;
  transition: all 0.2s ease;
}

.capture-button:active {
  background: rgba(255, 255, 255, 1) !important;
  transform: scale(0.95);
}

.capture-button:disabled {
  opacity: 0.6 !important;
  transform: none !important;
  cursor: not-allowed;
}

/* 響應式設計 */
@media (max-width: 480px) {
  .controls-top,
  .controls-bottom {
    padding: 16px;
  }

  .control-button {
    width: 48px !important;
    height: 48px !important;
    font-size: 20px !important;
  }

  .capture-button {
    width: 80px !important;
    height: 80px !important;
    font-size: 32px !important;
  }

  .camera-guides {
    top: 12%;
    bottom: 22%;
    left: 6%;
    right: 6%;
  }

  .guide-corner {
    width: 45px;
    height: 45px;
    border-width: 3px;
  }

  .hint-text {
    font-size: 14px;
    padding: 4px 8px;
  }

  .hint-subtext {
    font-size: 10px;
    padding: 2px 6px;
  }
}

/* 大屏幕優化 */
@media (min-width: 768px) {
  .capture-button {
    width: 100px !important;
    height: 100px !important;
    font-size: 40px !important;
  }

  .control-button {
    width: 56px !important;
    height: 56px !important;
    font-size: 24px !important;
  }

  .camera-guides {
    top: 8%;
    bottom: 18%;
    left: 4%;
    right: 4%;
  }
}

/* 動畫效果 */
.mobile-camera-modal {
  animation: fadeIn 0.3s ease-out;
}

/* iOS Safari 特殊處理 */
@supports (-webkit-appearance: none) {
  .mobile-camera-modal {
    height: 100vh;
    height: -webkit-fill-available;
  }
  
  .camera-container {
    height: 100vh;
    height: -webkit-fill-available;
  }
}

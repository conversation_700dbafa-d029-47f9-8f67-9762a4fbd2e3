/**
 * 名片詳情頁面 - 原生JavaScript版本
 */

class CardDetailPage extends Component {
  constructor(props = {}) {
    super(props);
    this.cardId = props.params?.id;
    this.state = {
      loading: true,
      saving: false,
      isEditing: false,
      cardData: {
        id: '',
        name: '',
        company_name: '',
        position: '',
        mobile_phone: '',
        office_phone: '',
        email: '',
        line_id: '',
        notes: '',
        company_address_1: '',
        company_address_2: '',
        created_at: '',
        updated_at: ''
      }
    };
  }

  render() {
    return `
      <div class="App">
        <!-- 導航欄 -->
        <div class="navbar">
          <button class="navbar-back" onclick="this.goBack()">
            <i class="fas fa-arrow-left"></i>
          </button>
          <div class="navbar-title">${this.state.isEditing ? '編輯名片' : '名片詳情'}</div>
          <div class="navbar-right">
            ${!this.state.loading ? `
              <button class="btn btn-default btn-small" onclick="this.toggleEditMode()">
                <i class="fas fa-${this.state.isEditing ? 'times' : 'edit'}"></i>
                ${this.state.isEditing ? '取消' : '編輯'}
              </button>
            ` : ''}
          </div>
        </div>

        <div class="content">
          ${this.state.loading ? this.renderLoading() : this.renderCardContent()}
        </div>
      </div>
    `;
  }

  renderLoading() {
    return `
      <div class="card">
        <div class="card-body text-center">
          <div class="loading-spinner" style="margin: 40px auto;"></div>
          <p style="color: var(--text-color-secondary);">載入中...</p>
        </div>
      </div>
    `;
  }

  renderCardContent() {
    return `
      <!-- 名片資料 -->
      <div class="card mb-md">
        <div class="card-header">
          <h3 class="card-title">
            <i class="fas fa-user-circle"></i> ${this.state.isEditing ? '編輯名片資料' : '名片資料'}
          </h3>
        </div>
        <div class="card-body">
          <div class="form">
            <!-- 基本資訊 -->
            <div class="divider-text">基本資訊</div>
            
            <div class="form-item">
              <label class="form-label required">姓名</label>
              ${this.renderField('name', 'text', '請輸入姓名')}
            </div>

            <div class="form-item">
              <label class="form-label">公司名稱</label>
              ${this.renderField('company_name', 'text', '請輸入公司名稱')}
            </div>

            <div class="form-item">
              <label class="form-label">職位</label>
              ${this.renderField('position', 'text', '請輸入職位')}
            </div>

            <!-- 聯絡資訊 -->
            <div class="divider-text">聯絡資訊</div>

            <div class="form-item">
              <label class="form-label">手機</label>
              ${this.renderField('mobile_phone', 'tel', '請輸入手機號碼')}
            </div>

            <div class="form-item">
              <label class="form-label">公司電話</label>
              ${this.renderField('office_phone', 'tel', '請輸入公司電話')}
            </div>

            <div class="form-item">
              <label class="form-label">Email</label>
              ${this.renderField('email', 'email', '請輸入Email地址')}
            </div>

            <div class="form-item">
              <label class="form-label">Line ID</label>
              ${this.renderField('line_id', 'text', '請輸入Line ID')}
            </div>

            <!-- 地址資訊 -->
            <div class="divider-text">地址資訊</div>

            <div class="form-item">
              <label class="form-label">公司地址一</label>
              ${this.renderField('company_address_1', 'text', '請輸入公司地址')}
            </div>

            <div class="form-item">
              <label class="form-label">公司地址二</label>
              ${this.renderField('company_address_2', 'text', '請輸入公司地址（續）')}
            </div>

            <!-- 備註 -->
            <div class="divider-text">其他資訊</div>

            <div class="form-item">
              <label class="form-label">備註</label>
              ${this.renderTextareaField('notes', '請輸入備註信息')}
            </div>

            <!-- 時間信息 -->
            ${this.state.cardData.created_at ? `
              <div class="divider-text">記錄信息</div>
              <div style="font-size: 14px; color: var(--text-color-secondary);">
                <p style="margin: 4px 0;">
                  <i class="fas fa-plus-circle" style="width: 16px; margin-right: 8px;"></i>
                  創建時間：${this.formatDateTime(this.state.cardData.created_at)}
                </p>
                ${this.state.cardData.updated_at ? `
                  <p style="margin: 4px 0;">
                    <i class="fas fa-edit" style="width: 16px; margin-right: 8px;"></i>
                    更新時間：${this.formatDateTime(this.state.cardData.updated_at)}
                  </p>
                ` : ''}
              </div>
            ` : ''}
          </div>
        </div>
      </div>

      <!-- 操作按鈕 -->
      ${this.state.isEditing ? `
        <div class="space space-vertical">
          <button 
            class="btn btn-success btn-large btn-block" 
            onclick="this.handleSave()"
            ${this.state.saving ? 'disabled' : ''}
          >
            <i class="fas fa-check icon"></i>
            ${this.state.saving ? '保存中...' : '保存更改'}
          </button>
          
          <button 
            class="btn btn-default btn-large btn-block" 
            onclick="this.cancelEdit()"
            ${this.state.saving ? 'disabled' : ''}
          >
            <i class="fas fa-times icon"></i>
            取消編輯
          </button>
        </div>
      ` : `
        <div class="space space-vertical">
          <button 
            class="btn btn-primary btn-large btn-block" 
            onclick="this.toggleEditMode()"
          >
            <i class="fas fa-edit icon"></i>
            編輯名片
          </button>
          
          <button 
            class="btn btn-danger btn-large btn-block" 
            onclick="this.deleteCard()"
          >
            <i class="fas fa-trash icon"></i>
            刪除名片
          </button>
        </div>
      `}

      <!-- 快速操作 -->
      ${!this.state.isEditing ? `
        <div class="card mt-md">
          <div class="card-header">
            <h3 class="card-title">
              <i class="fas fa-bolt"></i> 快速操作
            </h3>
          </div>
          <div class="card-body">
            <div class="space">
              ${this.state.cardData.mobile_phone ? `
                <button class="btn btn-default" onclick="this.callPhone('${this.state.cardData.mobile_phone}')">
                  <i class="fas fa-phone icon"></i>
                  撥打手機
                </button>
              ` : ''}
              
              ${this.state.cardData.email ? `
                <button class="btn btn-default" onclick="this.sendEmail('${this.state.cardData.email}')">
                  <i class="fas fa-envelope icon"></i>
                  發送郵件
                </button>
              ` : ''}
            </div>
          </div>
        </div>
      ` : ''}
    `;
  }

  renderField(field, type, placeholder) {
    const value = this.state.cardData[field] || '';
    
    if (this.state.isEditing) {
      return `
        <input 
          type="${type}" 
          class="form-input" 
          placeholder="${placeholder}" 
          value="${value}"
          onchange="this.updateCardData('${field}', this.value)"
        >
      `;
    } else {
      return `
        <div style="padding: 8px 0; font-size: 16px; color: var(--text-color);">
          ${value || '-'}
        </div>
      `;
    }
  }

  renderTextareaField(field, placeholder) {
    const value = this.state.cardData[field] || '';
    
    if (this.state.isEditing) {
      return `
        <textarea 
          class="form-textarea" 
          placeholder="${placeholder}" 
          rows="4"
          onchange="this.updateCardData('${field}', this.value)"
        >${value}</textarea>
      `;
    } else {
      return `
        <div style="padding: 8px 0; font-size: 16px; color: var(--text-color); white-space: pre-wrap;">
          ${value || '-'}
        </div>
      `;
    }
  }

  async mount() {
    await this.loadCardData();
    this.bindEvents();
  }

  bindEvents() {
    // 綁定所有onclick事件
    const buttons = document.querySelectorAll('button[onclick]');
    buttons.forEach(element => {
      const onclickStr = element.getAttribute('onclick');
      if (onclickStr) {
        element.onclick = (event) => {
          event.preventDefault();
          this.executeOnclickFunction(onclickStr, event);
        };
      }
    });

    // 綁定輸入事件
    const inputs = document.querySelectorAll('input[onchange], textarea[onchange]');
    inputs.forEach(element => {
      const onchangeStr = element.getAttribute('onchange');
      if (onchangeStr) {
        element.onchange = (event) => {
          this.executeOnchangeFunction(onchangeStr, event);
        };
      }
    });
  }

  executeOnclickFunction(funcStr, event) {
    try {
      const func = new Function('event', `return (${funcStr}).call(this, event)`);
      func.call(this, event);
    } catch (error) {
      console.error('執行onclick函數失敗:', error);
    }
  }

  executeOnchangeFunction(funcStr, event) {
    try {
      const func = new Function('event', `
        const target = event.target;
        const value = target.value;
        return (${funcStr}).call(target, event);
      `);
      func.call(this, event);
    } catch (error) {
      console.error('執行onchange函數失敗:', error);
    }
  }

  async loadCardData() {
    if (!this.cardId) {
      Toast.error('名片ID無效');
      router.navigate('/cards');
      return;
    }

    try {
      this.setState({ loading: true });
      const response = await cardsApi.getById(this.cardId);
      
      if (response) {
        this.setState({ 
          cardData: response,
          loading: false 
        });
      } else {
        Toast.error('名片不存在');
        router.navigate('/cards');
      }
    } catch (error) {
      console.error('載入名片失敗:', error);
      this.setState({ loading: false });
      errorHandler.showError(error);
      router.navigate('/cards');
    }
  }

  updateCardData(field, value) {
    const newCardData = { ...this.state.cardData };
    newCardData[field] = value;
    this.setState({ cardData: newCardData });
  }

  toggleEditMode() {
    if (this.state.isEditing) {
      this.cancelEdit();
    } else {
      this.setState({ isEditing: true });
    }
  }

  cancelEdit() {
    Dialog.confirm({
      title: '取消編輯',
      content: '是否取消編輯？未保存的更改將會丟失。',
      onConfirm: () => {
        this.setState({ isEditing: false });
        this.loadCardData(); // 重新載入原始資料
      }
    });
  }

  async handleSave() {
    // 驗證必填欄位
    if (!this.state.cardData.name.trim()) {
      Toast.error('請輸入姓名');
      return;
    }

    this.setState({ saving: true });

    try {
      const response = await cardsApi.update(this.cardId, this.state.cardData);

      if (response) {
        Toast.success('名片更新成功！');
        this.setState({ 
          isEditing: false,
          cardData: response
        });
      }
    } catch (error) {
      console.error('更新失敗:', error);
      errorHandler.showError(error);
    } finally {
      this.setState({ saving: false });
    }
  }

  async deleteCard() {
    const confirmed = await this.showConfirmDialog(
      '確認刪除',
      '確定要刪除這張名片嗎？此操作無法撤銷。'
    );

    if (!confirmed) return;

    try {
      await cardsApi.delete(this.cardId);
      Toast.success('名片刪除成功');
      router.navigate('/cards');
    } catch (error) {
      console.error('刪除名片失敗:', error);
      errorHandler.showError(error);
    }
  }

  callPhone(phoneNumber) {
    window.location.href = `tel:${phoneNumber}`;
  }

  sendEmail(email) {
    window.location.href = `mailto:${email}`;
  }

  showConfirmDialog(title, content) {
    return new Promise((resolve) => {
      Dialog.confirm({
        title,
        content,
        onConfirm: () => resolve(true),
        onCancel: () => resolve(false)
      });
    });
  }

  formatDateTime(dateString) {
    if (!dateString) return '';
    
    try {
      const date = new Date(dateString);
      return date.toLocaleString('zh-TW', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      return dateString;
    }
  }

  goBack() {
    if (this.state.isEditing) {
      this.cancelEdit();
    } else {
      router.navigate('/cards');
    }
  }

  unmount() {
    // 清理事件監聽器
  }
}

// 導出到全局
window.CardDetailPage = CardDetailPage;

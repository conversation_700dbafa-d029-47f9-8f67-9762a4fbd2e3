/**
 * 首頁組件 - 原生JavaScript版本
 */

class HomePage extends Component {
  constructor(props = {}) {
    super(props);
    this.state = {
      loading: false
    };
  }

  render() {
    return `
      <div class="App">
        <div class="home-container">
          <div class="home-card">
            <div class="home-header">
              <div class="app-logo">
                <i class="fas fa-id-card"></i>
              </div>
              <h1 class="app-title">名片 OCR 應用</h1>
              <p class="app-subtitle">智能名片掃描與管理</p>
            </div>

            <div class="home-actions">
              <button
                class="home-btn home-btn-primary"
                data-route="/scan"
                data-action="navigate"
              >
                <div class="btn-icon">
                  <i class="fas fa-camera"></i>
                </div>
                <div class="btn-content">
                  <div class="btn-title">開始掃描</div>
                  <div class="btn-subtitle">拍照或上傳名片</div>
                </div>
                <div class="btn-arrow">
                  <i class="fas fa-chevron-right"></i>
                </div>
              </button>

              <button
                class="home-btn home-btn-default"
                data-route="/cards"
                data-action="navigate"
              >
                <div class="btn-icon">
                  <i class="fas fa-address-book"></i>
                </div>
                <div class="btn-content">
                  <div class="btn-title">名片管理</div>
                  <div class="btn-subtitle">查看和編輯名片</div>
                </div>
                <div class="btn-arrow">
                  <i class="fas fa-chevron-right"></i>
                </div>
              </button>

              <button
                class="home-btn home-btn-secondary"
                data-route="/add-card"
                data-action="navigate"
              >
                <div class="btn-icon">
                  <i class="fas fa-plus"></i>
                </div>
                <div class="btn-content">
                  <div class="btn-title">手動新增</div>
                  <div class="btn-subtitle">直接輸入名片信息</div>
                </div>
                <div class="btn-arrow">
                  <i class="fas fa-chevron-right"></i>
                </div>
              </button>

              <button
                class="home-btn home-btn-warning"
                data-route="/camera-test"
                data-action="navigate"
              >
                <div class="btn-icon">
                  <i class="fas fa-video"></i>
                </div>
                <div class="btn-content">
                  <div class="btn-title">相機測試</div>
                  <div class="btn-subtitle">檢測設備兼容性</div>
                </div>
                <div class="btn-arrow">
                  <i class="fas fa-chevron-right"></i>
                </div>
              </button>
            </div>

            <div class="home-footer">
              <div class="feature-list">
                <div class="feature-item">
                  <i class="fas fa-check-circle"></i>
                  <span>OCR文字識別</span>
                </div>
                <div class="feature-item">
                  <i class="fas fa-check-circle"></i>
                  <span>智能信息提取</span>
                </div>
                <div class="feature-item">
                  <i class="fas fa-check-circle"></i>
                  <span>雲端同步存儲</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  mount() {
    // 綁定導航事件
    this.bindNavigationEvents();

    // 檢查設備兼容性
    this.checkDeviceCompatibility();

    // 添加頁面動畫
    this.addPageAnimations();
  }

  /**
   * 綁定導航事件
   */
  bindNavigationEvents() {
    const buttons = document.querySelectorAll('button[data-action="navigate"]');
    buttons.forEach(button => {
      button.addEventListener('click', (event) => {
        event.preventDefault();
        const path = button.getAttribute('data-route');
        if (path) {
          this.handleNavigation(path, event);
        }
      });
    });
  }

  /**
   * 添加頁面動畫
   */
  addPageAnimations() {
    const homeCard = document.querySelector('.home-card');
    const homeButtons = document.querySelectorAll('.home-btn');

    if (homeCard) {
      homeCard.style.opacity = '0';
      homeCard.style.transform = 'translateY(20px)';

      setTimeout(() => {
        homeCard.style.transition = 'all 0.6s ease-out';
        homeCard.style.opacity = '1';
        homeCard.style.transform = 'translateY(0)';
      }, 100);
    }

    // 按鈕依次出現動畫
    homeButtons.forEach((button, index) => {
      button.style.opacity = '0';
      button.style.transform = 'translateX(-20px)';

      setTimeout(() => {
        button.style.transition = 'all 0.4s ease-out';
        button.style.opacity = '1';
        button.style.transform = 'translateX(0)';
      }, 200 + index * 100);
    });
  }

  /**
   * 處理導航
   */
  handleNavigation(path, event) {
    if (this.state.loading) return;

    this.setState({ loading: true });

    // 添加點擊反饋
    const button = event?.target?.closest('button');
    if (button) {
      button.style.transform = 'scale(0.98)';
      setTimeout(() => {
        button.style.transform = '';
      }, 150);
    }

    // 延遲導航以顯示點擊效果
    setTimeout(() => {
      router.navigate(path);
      this.setState({ loading: false });
    }, 200);
  }

  /**
   * 檢查設備兼容性
   */
  async checkDeviceCompatibility() {
    try {
      const envInfo = await getEnvironmentInfo();
      
      // 檢查相機支持
      if (!envInfo.camera.hasCamera) {
        this.showCompatibilityWarning('相機功能不可用，部分功能可能受限');
      }
      
      // 檢查現代瀏覽器特性
      if (!window.fetch || !window.Promise) {
        this.showCompatibilityWarning('瀏覽器版本過舊，建議升級瀏覽器以獲得最佳體驗');
      }
      
      // 移動端特殊提示
      if (envInfo.isMobile && !envInfo.camera.supportsFacingMode) {
        console.warn('設備不支持攝像頭切換功能');
      }
      
    } catch (error) {
      console.error('設備兼容性檢查失敗:', error);
    }
  }

  /**
   * 顯示兼容性警告
   */
  showCompatibilityWarning(message) {
    // 創建警告提示
    const warning = document.createElement('div');
    warning.style.cssText = `
      position: fixed;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      background: var(--warning-color);
      color: white;
      padding: 12px 20px;
      border-radius: var(--border-radius);
      font-size: 14px;
      z-index: 1000;
      max-width: 90%;
      text-align: center;
      box-shadow: var(--shadow-medium);
    `;
    warning.innerHTML = `
      <i class="fas fa-exclamation-triangle" style="margin-right: 8px;"></i>
      ${message}
    `;
    
    document.body.appendChild(warning);
    
    // 5秒後自動移除
    setTimeout(() => {
      if (warning.parentNode) {
        warning.parentNode.removeChild(warning);
      }
    }, 5000);
  }

  unmount() {
    // 清理事件監聽器
    const buttons = document.querySelectorAll('button[onclick*="handleNavigation"]');
    buttons.forEach(button => {
      button.onclick = null;
    });
  }
}

// 導出到全局
window.HomePage = HomePage;

/**
 * UI組件庫 - 替代antd-mobile
 */

// 基礎組件類
class Component {
  constructor(props = {}) {
    this.props = props;
    this.state = {};
    this.element = null;
  }

  setState(newState) {
    this.state = { ...this.state, ...newState };
    this.update();
  }

  render() {
    return '';
  }

  mount() {
    // 組件掛載後的邏輯
  }

  unmount() {
    // 組件卸載前的清理
  }

  update() {
    // 重新渲染組件
    if (this.element) {
      const newHtml = this.render();
      this.element.outerHTML = newHtml;
    }
  }
}

// Toast 通知組件
class Toast {
  static activeToasts = new Set();
  static maxToasts = 3;

  static show(options) {
    const {
      content,
      type = 'default',
      duration = 3000,
      position = 'center'
    } = options;

    const container = document.getElementById('toast-container');
    if (!container) return;

    // 限制同時顯示的Toast數量
    if (this.activeToasts.size >= this.maxToasts) {
      const oldestToast = this.activeToasts.values().next().value;
      this.removeToast(oldestToast);
    }

    const toast = document.createElement('div');
    toast.className = `toast ${type}`;

    // 添加圖標
    const icon = this.getIcon(type);
    toast.innerHTML = `
      <div class="toast-content">
        ${icon ? `<i class="${icon}"></i>` : ''}
        <span class="toast-text">${content}</span>
      </div>
    `;

    // 添加關閉按鈕
    const closeBtn = document.createElement('button');
    closeBtn.className = 'toast-close';
    closeBtn.innerHTML = '<i class="fas fa-times"></i>';
    closeBtn.onclick = () => this.removeToast(toast);
    toast.appendChild(closeBtn);

    container.appendChild(toast);
    this.activeToasts.add(toast);

    // 添加進入動畫
    setTimeout(() => {
      toast.classList.add('toast-show');
    }, 10);

    // 自動移除
    if (duration > 0) {
      setTimeout(() => {
        this.removeToast(toast);
      }, duration);
    }

    return toast;
  }

  static removeToast(toast) {
    if (!toast || !toast.parentNode) return;

    toast.classList.add('toast-hide');
    this.activeToasts.delete(toast);

    setTimeout(() => {
      if (toast.parentNode) {
        toast.parentNode.removeChild(toast);
      }
    }, 300);
  }

  static getIcon(type) {
    const icons = {
      success: 'fas fa-check-circle',
      warning: 'fas fa-exclamation-triangle',
      error: 'fas fa-times-circle',
      info: 'fas fa-info-circle'
    };
    return icons[type] || null;
  }

  static success(content, duration = 3000) {
    return Toast.show({ content, type: 'success', duration });
  }

  static warning(content, duration = 4000) {
    return Toast.show({ content, type: 'warning', duration });
  }

  static error(content, duration = 5000) {
    return Toast.show({ content, type: 'error', duration });
  }

  static info(content, duration = 3000) {
    return Toast.show({ content, type: 'info', duration });
  }

  static clear() {
    this.activeToasts.forEach(toast => this.removeToast(toast));
  }
}

// Modal 模態框組件
class Modal {
  constructor(options = {}) {
    this.options = {
      title: '',
      content: '',
      showClose: true,
      onClose: null,
      ...options
    };
    this.element = null;
    this.visible = false;
  }

  show() {
    if (this.visible) return;

    const container = document.getElementById('modal-container');
    if (!container) return;

    this.element = document.createElement('div');
    this.element.className = 'modal-container visible';
    this.element.innerHTML = this.render();

    container.appendChild(this.element);
    this.visible = true;

    // 綁定事件
    this.bindEvents();

    // 阻止背景滾動
    document.body.style.overflow = 'hidden';
  }

  hide() {
    if (!this.visible || !this.element) return;

    this.element.classList.remove('visible');
    
    setTimeout(() => {
      if (this.element && this.element.parentNode) {
        this.element.parentNode.removeChild(this.element);
      }
      this.element = null;
      this.visible = false;
      
      // 恢復背景滾動
      document.body.style.overflow = '';
      
      if (this.options.onClose) {
        this.options.onClose();
      }
    }, 300);
  }

  render() {
    return `
      <div class="modal-backdrop" data-modal-backdrop></div>
      <div class="modal">
        ${this.options.title ? `
          <div class="modal-header">
            <h3 class="modal-title">${this.options.title}</h3>
            ${this.options.showClose ? `
              <button class="modal-close" data-modal-close>
                <i class="fas fa-times"></i>
              </button>
            ` : ''}
          </div>
        ` : ''}
        <div class="modal-body">
          ${this.options.content}
        </div>
      </div>
    `;
  }

  bindEvents() {
    if (!this.element) return;

    // 點擊背景關閉
    const backdrop = this.element.querySelector('[data-modal-backdrop]');
    if (backdrop) {
      backdrop.addEventListener('click', () => this.hide());
    }

    // 點擊關閉按鈕
    const closeBtn = this.element.querySelector('[data-modal-close]');
    if (closeBtn) {
      closeBtn.addEventListener('click', () => this.hide());
    }

    // ESC鍵關閉
    const handleKeydown = (event) => {
      if (event.key === 'Escape') {
        this.hide();
        document.removeEventListener('keydown', handleKeydown);
      }
    };
    document.addEventListener('keydown', handleKeydown);
  }

  static show(options) {
    const modal = new Modal(options);
    modal.show();
    return modal;
  }
}

// Dialog 對話框組件
class Dialog {
  static confirm(options) {
    const {
      title = '確認',
      content = '',
      onConfirm = null,
      onCancel = null,
      confirmText = '確認',
      cancelText = '取消'
    } = options;

    const modal = new Modal({
      title,
      content: `
        <div style="margin-bottom: 24px;">${content}</div>
        <div class="modal-footer">
          <button class="btn btn-default" data-dialog-cancel>${cancelText}</button>
          <button class="btn btn-primary" data-dialog-confirm>${confirmText}</button>
        </div>
      `,
      showClose: false,
      onClose: onCancel
    });

    modal.show();

    // 綁定按鈕事件
    setTimeout(() => {
      const confirmBtn = modal.element?.querySelector('[data-dialog-confirm]');
      const cancelBtn = modal.element?.querySelector('[data-dialog-cancel]');

      if (confirmBtn) {
        confirmBtn.addEventListener('click', () => {
          modal.hide();
          if (onConfirm) onConfirm();
        });
      }

      if (cancelBtn) {
        cancelBtn.addEventListener('click', () => {
          modal.hide();
          if (onCancel) onCancel();
        });
      }
    }, 0);

    return modal;
  }

  static alert(options) {
    const {
      title = '提示',
      content = '',
      onConfirm = null,
      confirmText = '確認'
    } = options;

    return Dialog.confirm({
      title,
      content,
      onConfirm,
      confirmText,
      onCancel: onConfirm,
      cancelText: ''
    });
  }
}

// Loading 加載組件
class Loading {
  static show(text = '載入中...') {
    const existing = document.getElementById('global-loading');
    if (existing) return;

    const loading = document.createElement('div');
    loading.id = 'global-loading';
    loading.className = 'loading-container';
    loading.innerHTML = `
      <div class="loading-spinner"></div>
      <div class="loading-text">${text}</div>
    `;

    document.body.appendChild(loading);
  }

  static hide() {
    const loading = document.getElementById('global-loading');
    if (loading) {
      loading.parentNode.removeChild(loading);
    }
  }
}

// 工具函數：創建按鈕
function createButton(options) {
  const {
    text = '',
    type = 'default',
    size = 'medium',
    block = false,
    disabled = false,
    icon = '',
    onClick = null,
    className = ''
  } = options;

  const classes = [
    'btn',
    `btn-${type}`,
    `btn-${size}`,
    block ? 'btn-block' : '',
    className
  ].filter(Boolean).join(' ');

  const button = document.createElement('button');
  button.className = classes;
  button.disabled = disabled;
  
  if (icon) {
    button.innerHTML = `<i class="${icon}"></i> ${text}`;
  } else {
    button.textContent = text;
  }

  if (onClick) {
    button.addEventListener('click', onClick);
  }

  return button;
}

// 工具函數：創建卡片
function createCard(options) {
  const {
    title = '',
    content = '',
    extra = '',
    className = ''
  } = options;

  const card = document.createElement('div');
  card.className = `card ${className}`;

  let html = '';
  
  if (title || extra) {
    html += `
      <div class="card-header">
        <h3 class="card-title">${title}</h3>
        ${extra ? `<div class="card-extra">${extra}</div>` : ''}
      </div>
    `;
  }

  html += `<div class="card-body">${content}</div>`;
  
  card.innerHTML = html;
  return card;
}

// 工具函數：創建表單項
function createFormItem(options) {
  const {
    label = '',
    required = false,
    content = '',
    className = ''
  } = options;

  const formItem = document.createElement('div');
  formItem.className = `form-item ${className}`;

  let html = '';
  
  if (label) {
    html += `<label class="form-label ${required ? 'required' : ''}">${label}</label>`;
  }
  
  html += content;
  
  formItem.innerHTML = html;
  return formItem;
}

// 工具函數：創建輸入框
function createInput(options) {
  const {
    type = 'text',
    placeholder = '',
    value = '',
    className = '',
    onChange = null
  } = options;

  const input = document.createElement('input');
  input.type = type;
  input.className = `form-input ${className}`;
  input.placeholder = placeholder;
  input.value = value;

  if (onChange) {
    input.addEventListener('input', (e) => onChange(e.target.value));
  }

  return input;
}

// 工具函數：創建文本域
function createTextarea(options) {
  const {
    placeholder = '',
    value = '',
    rows = 4,
    className = '',
    onChange = null
  } = options;

  const textarea = document.createElement('textarea');
  textarea.className = `form-textarea ${className}`;
  textarea.placeholder = placeholder;
  textarea.value = value;
  textarea.rows = rows;

  if (onChange) {
    textarea.addEventListener('input', (e) => onChange(e.target.value));
  }

  return textarea;
}

// 導出到全局
window.Component = Component;
window.Toast = Toast;
window.Modal = Modal;
window.Dialog = Dialog;
window.Loading = Loading;
window.createButton = createButton;
window.createCard = createCard;
window.createFormItem = createFormItem;
window.createInput = createInput;
window.createTextarea = createTextarea;

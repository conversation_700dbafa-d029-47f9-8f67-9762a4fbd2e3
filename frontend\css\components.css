/* UI 組件樣式 - 替代 antd-mobile */

/* 按鈕組件 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--font-size-base);
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  outline: none;
  user-select: none;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 按鈕尺寸 */
.btn-small {
  padding: 4px 8px;
  font-size: var(--font-size-small);
  min-height: 28px;
}

.btn-medium {
  padding: 8px 16px;
  font-size: var(--font-size-base);
  min-height: 36px;
}

.btn-large {
  padding: 12px 24px;
  font-size: var(--font-size-xl);
  min-height: 48px;
}

.btn-block {
  width: 100%;
  display: flex;
}

/* 按鈕類型 */
.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: var(--primary-hover);
}

.btn-primary:active:not(:disabled) {
  background: var(--primary-active);
}

.btn-default {
  background: var(--background-color);
  color: var(--text-color);
  border: 1px solid var(--border-color);
}

.btn-default:hover:not(:disabled) {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.btn-warning {
  background: var(--warning-color);
  color: white;
}

.btn-warning:hover:not(:disabled) {
  background: #d48806;
}

.btn-success {
  background: var(--success-color);
  color: white;
}

.btn-success:hover:not(:disabled) {
  background: #389e0d;
}

.btn-danger {
  background: var(--error-color);
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: #d4380d;
}

/* 卡片組件 */
.card {
  background: var(--background-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  overflow: hidden;
  margin-bottom: var(--spacing-md);
}

.card-header {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  font-size: var(--font-size-large);
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.card-extra {
  color: var(--text-color-secondary);
}

.card-body {
  padding: var(--spacing-md);
}

/* 空間組件 */
.space {
  display: flex;
  gap: var(--spacing-sm);
}

.space-vertical {
  flex-direction: column;
}

.space-small {
  gap: var(--spacing-xs);
}

.space-large {
  gap: var(--spacing-md);
}

/* 導航欄組件 */
.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  background: var(--background-color);
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 100;
}

.navbar-back {
  background: none;
  border: none;
  font-size: var(--font-size-xl);
  color: var(--primary-color);
  cursor: pointer;
  padding: var(--spacing-xs);
}

.navbar-title {
  font-size: var(--font-size-large);
  font-weight: 600;
  color: var(--text-color);
  flex: 1;
  text-align: center;
}

.navbar-right {
  min-width: 40px;
  display: flex;
  justify-content: flex-end;
}

/* 表單組件 */
.form {
  width: 100%;
}

.form-item {
  margin-bottom: var(--spacing-md);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-size: var(--font-size-base);
  color: var(--text-color);
  font-weight: 500;
}

.form-label.required::after {
  content: ' *';
  color: var(--error-color);
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: var(--font-size-base);
  color: var(--text-color);
  background: var(--background-color);
  transition: border-color 0.2s ease;
  outline: none;
}

.form-input:focus,
.form-textarea:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: var(--text-color-disabled);
}

/* 搜索欄組件 */
.search-bar {
  position: relative;
  margin-bottom: var(--spacing-md);
}

.search-input {
  width: 100%;
  padding: 8px 12px 8px 40px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: var(--font-size-base);
  background: var(--background-color);
  outline: none;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  border-color: var(--primary-color);
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-color-secondary);
  font-size: var(--font-size-base);
}

/* 分割線組件 */
.divider {
  border: none;
  border-top: 1px solid var(--border-color);
  margin: var(--spacing-md) 0;
}

.divider-text {
  position: relative;
  text-align: center;
  background: var(--background-color);
  padding: 0 var(--spacing-md);
  color: var(--text-color-secondary);
  font-size: var(--font-size-small);
}

.divider-text::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--border-color);
  z-index: -1;
}

/* Toast 通知組件 */
.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  pointer-events: none;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.toast {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  color: var(--text-color);
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  font-size: var(--font-size-base);
  max-width: 350px;
  min-width: 280px;
  box-shadow: var(--shadow-medium);
  border-left: 4px solid var(--border-color);
  pointer-events: auto;
  opacity: 0;
  transform: translateX(100%);
  transition: all 0.3s ease-out;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.toast.toast-show {
  opacity: 1;
  transform: translateX(0);
}

.toast.toast-hide {
  opacity: 0;
  transform: translateX(100%);
}

.toast.success {
  border-left-color: var(--success-color);
  background: rgba(240, 255, 240, 0.95);
}

.toast.warning {
  border-left-color: var(--warning-color);
  background: rgba(255, 251, 230, 0.95);
}

.toast.error {
  border-left-color: var(--error-color);
  background: rgba(255, 242, 240, 0.95);
}

.toast.info {
  border-left-color: var(--primary-color);
  background: rgba(240, 247, 255, 0.95);
}

.toast-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.toast-content i {
  margin-right: var(--spacing-sm);
  font-size: var(--font-size-large);
}

.toast.success .toast-content i {
  color: var(--success-color);
}

.toast.warning .toast-content i {
  color: var(--warning-color);
}

.toast.error .toast-content i {
  color: var(--error-color);
}

.toast.info .toast-content i {
  color: var(--primary-color);
}

.toast-text {
  flex: 1;
  line-height: 1.4;
}

.toast-close {
  background: none;
  border: none;
  color: var(--text-color-secondary);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: var(--spacing-sm);
  transition: all 0.2s ease;
}

.toast-close:hover {
  background: rgba(0, 0, 0, 0.1);
  color: var(--text-color);
}

/* 移動端Toast優化 */
@media (max-width: 768px) {
  .toast-container {
    top: 10px;
    right: 10px;
    left: 10px;
    align-items: stretch;
  }

  .toast {
    max-width: none;
    min-width: auto;
  }
}

/* Modal 模態框組件 */
.modal-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: none;
}

.modal-container.visible {
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  animation: fadeIn 0.3s ease-out;
}

.modal {
  background: var(--background-color);
  border-radius: var(--border-radius-large);
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
  position: relative;
  z-index: 1;
  animation: modalSlideIn 0.3s ease-out;
}

.modal-header {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  font-size: var(--font-size-large);
  font-weight: 600;
  color: var(--text-color);
}

.modal-close {
  background: none;
  border: none;
  font-size: var(--font-size-xl);
  color: var(--text-color-secondary);
  cursor: pointer;
  padding: var(--spacing-xs);
}

.modal-body {
  padding: var(--spacing-md);
}

.modal-footer {
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-color);
  display: flex;
  gap: var(--spacing-sm);
  justify-content: flex-end;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 圖標樣式 */
.icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-xs);
}

.icon-only {
  margin-right: 0;
}

/* 導航欄組件 */
.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  background: var(--background-color);
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.navbar-back {
  background: none;
  border: none;
  color: var(--primary-color);
  font-size: var(--font-size-xl);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: 50%;
  transition: all 0.2s ease;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navbar-back:hover {
  background: var(--background-light);
  transform: scale(1.1);
}

.navbar-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-color);
  flex: 1;
  text-align: center;
}

.navbar-right {
  width: 40px;
  display: flex;
  justify-content: flex-end;
}

/* 內容區域 */
.content {
  padding: var(--spacing-md);
  max-width: 800px;
  margin: 0 auto;
}

/* 上傳區域樣式 */
.upload-area {
  border: 2px dashed var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-xl);
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: var(--background-light);
  min-height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-area:hover {
  border-color: var(--primary-color);
  background: rgba(102, 126, 234, 0.05);
}

.upload-text {
  color: var(--text-color-secondary);
  font-size: var(--font-size-base);
  margin-top: var(--spacing-sm);
}

/* 預覽圖片樣式 */
.preview-image {
  width: 100%;
  max-height: 300px;
  object-fit: contain;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-medium);
}

.camera-container {
  position: relative;
  margin: var(--spacing-md) 0;
}

/* 名片網格樣式 */
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-md);
  margin-top: var(--spacing-md);
}

.card-item {
  cursor: pointer;
  transition: all 0.2s ease;
}

.card-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-heavy);
}

/* 響應式調整 */
@media (max-width: 768px) {
  .btn-large {
    padding: 10px 20px;
    font-size: var(--font-size-large);
    min-height: 44px;
  }

  .card-header,
  .card-body {
    padding: var(--spacing-sm);
  }

  .navbar {
    padding: var(--spacing-sm) var(--spacing-md);
  }

  .modal {
    max-width: 95vw;
    margin: var(--spacing-md);
  }

  .content {
    padding: var(--spacing-sm);
  }

  .card-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }

  .upload-area {
    min-height: 150px;
    padding: var(--spacing-lg);
  }
}

/* UI 組件樣式 - 替代 antd-mobile */

/* 按鈕組件 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--font-size-base);
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  outline: none;
  user-select: none;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 按鈕尺寸 */
.btn-small {
  padding: 4px 8px;
  font-size: var(--font-size-small);
  min-height: 28px;
}

.btn-medium {
  padding: 8px 16px;
  font-size: var(--font-size-base);
  min-height: 36px;
}

.btn-large {
  padding: 12px 24px;
  font-size: var(--font-size-xl);
  min-height: 48px;
}

.btn-block {
  width: 100%;
  display: flex;
}

/* 按鈕類型 */
.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: var(--primary-hover);
}

.btn-primary:active:not(:disabled) {
  background: var(--primary-active);
}

.btn-default {
  background: var(--background-color);
  color: var(--text-color);
  border: 1px solid var(--border-color);
}

.btn-default:hover:not(:disabled) {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.btn-warning {
  background: var(--warning-color);
  color: white;
}

.btn-warning:hover:not(:disabled) {
  background: #d48806;
}

.btn-success {
  background: var(--success-color);
  color: white;
}

.btn-success:hover:not(:disabled) {
  background: #389e0d;
}

.btn-danger {
  background: var(--error-color);
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: #d4380d;
}

/* 卡片組件 */
.card {
  background: var(--background-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  overflow: hidden;
  margin-bottom: var(--spacing-md);
}

.card-header {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  font-size: var(--font-size-large);
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.card-extra {
  color: var(--text-color-secondary);
}

.card-body {
  padding: var(--spacing-md);
}

/* 空間組件 */
.space {
  display: flex;
  gap: var(--spacing-sm);
}

.space-vertical {
  flex-direction: column;
}

.space-small {
  gap: var(--spacing-xs);
}

.space-large {
  gap: var(--spacing-md);
}

/* 導航欄組件 */
.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  background: var(--background-color);
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 100;
}

.navbar-back {
  background: none;
  border: none;
  font-size: var(--font-size-xl);
  color: var(--primary-color);
  cursor: pointer;
  padding: var(--spacing-xs);
}

.navbar-title {
  font-size: var(--font-size-large);
  font-weight: 600;
  color: var(--text-color);
  flex: 1;
  text-align: center;
}

.navbar-right {
  min-width: 40px;
  display: flex;
  justify-content: flex-end;
}

/* 表單組件 */
.form {
  width: 100%;
}

.form-item {
  margin-bottom: var(--spacing-md);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-size: var(--font-size-base);
  color: var(--text-color);
  font-weight: 500;
}

.form-label.required::after {
  content: ' *';
  color: var(--error-color);
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: var(--font-size-base);
  color: var(--text-color);
  background: var(--background-color);
  transition: border-color 0.2s ease;
  outline: none;
}

.form-input:focus,
.form-textarea:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: var(--text-color-disabled);
}

/* 搜索欄組件 */
.search-bar {
  position: relative;
  margin-bottom: var(--spacing-md);
}

.search-input {
  width: 100%;
  padding: 8px 12px 8px 40px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: var(--font-size-base);
  background: var(--background-color);
  outline: none;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  border-color: var(--primary-color);
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-color-secondary);
  font-size: var(--font-size-base);
}

/* 分割線組件 */
.divider {
  border: none;
  border-top: 1px solid var(--border-color);
  margin: var(--spacing-md) 0;
}

.divider-text {
  position: relative;
  text-align: center;
  background: var(--background-color);
  padding: 0 var(--spacing-md);
  color: var(--text-color-secondary);
  font-size: var(--font-size-small);
}

.divider-text::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--border-color);
  z-index: -1;
}

/* Toast 通知組件 */
.toast-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 9999;
  pointer-events: none;
}

.toast {
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 12px 20px;
  border-radius: var(--border-radius);
  font-size: var(--font-size-base);
  max-width: 300px;
  text-align: center;
  animation: toastFadeIn 0.3s ease-out;
  pointer-events: auto;
}

.toast.success {
  background: var(--success-color);
}

.toast.warning {
  background: var(--warning-color);
}

.toast.error {
  background: var(--error-color);
}

@keyframes toastFadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Modal 模態框組件 */
.modal-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: none;
}

.modal-container.visible {
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  animation: fadeIn 0.3s ease-out;
}

.modal {
  background: var(--background-color);
  border-radius: var(--border-radius-large);
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
  position: relative;
  z-index: 1;
  animation: modalSlideIn 0.3s ease-out;
}

.modal-header {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  font-size: var(--font-size-large);
  font-weight: 600;
  color: var(--text-color);
}

.modal-close {
  background: none;
  border: none;
  font-size: var(--font-size-xl);
  color: var(--text-color-secondary);
  cursor: pointer;
  padding: var(--spacing-xs);
}

.modal-body {
  padding: var(--spacing-md);
}

.modal-footer {
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-color);
  display: flex;
  gap: var(--spacing-sm);
  justify-content: flex-end;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 圖標樣式 */
.icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-xs);
}

.icon-only {
  margin-right: 0;
}

/* 響應式調整 */
@media (max-width: 768px) {
  .btn-large {
    padding: 10px 20px;
    font-size: var(--font-size-large);
    min-height: 44px;
  }
  
  .card-header,
  .card-body {
    padding: var(--spacing-sm);
  }
  
  .navbar {
    padding: var(--spacing-sm) var(--spacing-md);
  }
  
  .modal {
    max-width: 95vw;
    margin: var(--spacing-md);
  }
}

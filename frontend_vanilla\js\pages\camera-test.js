/**
 * 相機測試頁面 - 原生JavaScript版本
 */

class CameraTestPage extends Component {
  constructor(props = {}) {
    super(props);
    this.state = {
      cameraManager: null,
      deviceInfo: null,
      cameraStatus: '未初始化',
      showMobileCamera: false,
      capturedImage: null,
      isMobile: false,
      testResults: []
    };
    
    this.mobileCameraModal = null;
  }

  render() {
    return `
      <div class="App">
        <!-- 導航欄 -->
        <div class="navbar">
          <button class="navbar-back" onclick="this.goBack()">
            <i class="fas fa-arrow-left"></i>
          </button>
          <div class="navbar-title">相機系統測試</div>
          <div class="navbar-right"></div>
        </div>

        <div class="content">
          <!-- 系統狀態 -->
          <div class="card mb-md">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-info-circle"></i> 系統狀態
              </h3>
            </div>
            <div class="card-body">
              <div style="margin-bottom: 16px;">
                <strong>相機狀態：</strong>
                <span style="color: ${this.getStatusColor()};">${this.state.cameraStatus}</span>
              </div>
              
              ${this.state.deviceInfo ? `
                <div style="margin-bottom: 16px;">
                  <strong>設備類型：</strong> ${this.state.deviceInfo.deviceType}
                </div>
                <div style="margin-bottom: 16px;">
                  <strong>推薦模式：</strong> ${this.state.deviceInfo.recommendedMode}
                </div>
                <div style="margin-bottom: 16px;">
                  <strong>相機支持：</strong> 
                  <span style="color: ${this.state.deviceInfo.camera.hasCamera ? 'var(--success-color)' : 'var(--error-color)'};">
                    ${this.state.deviceInfo.camera.hasCamera ? '✓ 支持' : '✗ 不支持'}
                  </span>
                </div>
                <div style="margin-bottom: 16px;">
                  <strong>攝像頭切換：</strong> 
                  <span style="color: ${this.state.deviceInfo.camera.supportsFacingMode ? 'var(--success-color)' : 'var(--warning-color)'};">
                    ${this.state.deviceInfo.camera.supportsFacingMode ? '✓ 支持' : '✗ 不支持'}
                  </span>
                </div>
              ` : ''}
            </div>
          </div>

          <!-- 測試操作 -->
          <div class="card mb-md">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-play-circle"></i> 測試操作
              </h3>
            </div>
            <div class="card-body">
              <div class="space space-vertical">
                <button 
                  class="btn btn-primary btn-large btn-block" 
                  onclick="this.initializeCamera()"
                >
                  <i class="fas fa-power-off icon"></i>
                  初始化相機系統
                </button>
                
                <button 
                  class="btn btn-success btn-large btn-block" 
                  onclick="this.testCamera()"
                  ${!this.state.cameraManager ? 'disabled' : ''}
                >
                  <i class="fas fa-camera icon"></i>
                  測試相機拍照
                </button>
                
                ${this.state.isMobile ? `
                  <button 
                    class="btn btn-warning btn-large btn-block" 
                    onclick="this.testCameraSwitch()"
                    ${!this.state.cameraManager ? 'disabled' : ''}
                  >
                    <i class="fas fa-sync-alt icon"></i>
                    測試攝像頭切換
                  </button>
                ` : ''}
                
                <button 
                  class="btn btn-default btn-large btn-block" 
                  onclick="this.runFullTest()"
                >
                  <i class="fas fa-clipboard-check icon"></i>
                  運行完整測試
                </button>
              </div>
            </div>
          </div>

          <!-- 測試結果 -->
          ${this.renderTestResults()}

          <!-- 拍攝結果 -->
          ${this.renderCapturedImage()}

          <!-- 設備詳細信息 -->
          ${this.renderDeviceInfo()}
        </div>
      </div>
    `;
  }

  renderTestResults() {
    if (this.state.testResults.length === 0) {
      return '';
    }

    return `
      <div class="card mb-md">
        <div class="card-header">
          <h3 class="card-title">
            <i class="fas fa-list-alt"></i> 測試結果
          </h3>
        </div>
        <div class="card-body">
          ${this.state.testResults.map(result => `
            <div style="margin-bottom: 12px; padding: 8px; border-left: 4px solid ${result.success ? 'var(--success-color)' : 'var(--error-color)'}; background: ${result.success ? '#f6ffed' : '#fff2f0'};">
              <div style="font-weight: 600; margin-bottom: 4px;">
                <i class="fas fa-${result.success ? 'check' : 'times'}" style="color: ${result.success ? 'var(--success-color)' : 'var(--error-color)'}; margin-right: 8px;"></i>
                ${result.name}
              </div>
              <div style="font-size: 14px; color: var(--text-color-secondary);">
                ${result.message}
              </div>
              ${result.details ? `
                <div style="font-size: 12px; color: var(--text-color-disabled); margin-top: 4px;">
                  ${result.details}
                </div>
              ` : ''}
            </div>
          `).join('')}
        </div>
      </div>
    `;
  }

  renderCapturedImage() {
    if (!this.state.capturedImage) {
      return '';
    }

    return `
      <div class="card mb-md">
        <div class="card-header">
          <h3 class="card-title">
            <i class="fas fa-image"></i> 拍攝結果
          </h3>
        </div>
        <div class="card-body">
          <img
            src="${this.state.capturedImage}"
            alt="拍攝的照片"
            style="width: 100%; height: 350px; object-fit: cover; border-radius: 8px; box-shadow: var(--shadow-medium);"
          />
          <div style="margin-top: 12px; text-align: center;">
            <button class="btn btn-default" onclick="this.clearCapturedImage()">
              <i class="fas fa-trash icon"></i>
              清除圖片
            </button>
          </div>
        </div>
      </div>
    `;
  }

  renderDeviceInfo() {
    if (!this.state.deviceInfo) {
      return '';
    }

    return `
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">
            <i class="fas fa-mobile-alt"></i> 詳細設備信息
          </h3>
        </div>
        <div class="card-body">
          <pre style="font-size: 12px; background: var(--background-light); padding: 12px; border-radius: 4px; overflow: auto; white-space: pre-wrap;">
${JSON.stringify(this.state.deviceInfo, null, 2)}
          </pre>
        </div>
      </div>
    `;
  }

  async mount() {
    // 檢測設備信息
    await this.detectDevice();
    
    // 綁定事件
    this.bindEvents();
  }

  bindEvents() {
    // 綁定所有onclick事件
    const buttons = document.querySelectorAll('button[onclick]');
    buttons.forEach(element => {
      const onclickStr = element.getAttribute('onclick');
      if (onclickStr) {
        element.onclick = (event) => {
          event.preventDefault();
          this.executeOnclickFunction(onclickStr, event);
        };
      }
    });
  }

  executeOnclickFunction(funcStr, event) {
    try {
      const func = new Function('event', `return (${funcStr}).call(this, event)`);
      func.call(this, event);
    } catch (error) {
      console.error('執行onclick函數失敗:', error);
    }
  }

  async detectDevice() {
    try {
      const deviceInfo = await getEnvironmentInfo();
      const recommendedMode = await getRecommendedCameraMode();
      
      this.setState({
        deviceInfo: {
          ...deviceInfo,
          recommendedMode
        },
        isMobile: deviceInfo.isMobile
      });
      
      this.addTestResult('設備檢測', true, `設備類型: ${deviceInfo.deviceType}, 推薦模式: ${recommendedMode}`);
    } catch (error) {
      console.error('設備檢測失敗:', error);
      this.addTestResult('設備檢測', false, error.message);
    }
  }

  async initializeCamera() {
    try {
      this.setState({ cameraStatus: '正在初始化...' });
      
      const cameraManager = getCameraManager();
      
      if (!cameraManager.isInitialized) {
        await cameraManager.initialize();
      }
      
      this.setState({ 
        cameraManager,
        cameraStatus: `相機已初始化 (${cameraManager.getMode()}模式)`
      });
      
      this.addTestResult('相機初始化', true, `模式: ${cameraManager.getMode()}`);
      
      Toast.success('相機系統初始化成功！');
      
    } catch (error) {
      console.error('相機初始化失敗:', error);
      this.setState({ cameraStatus: '初始化失敗' });
      this.addTestResult('相機初始化', false, error.message);
      Toast.error('相機初始化失敗');
    }
  }

  async testCamera() {
    if (!this.state.cameraManager) {
      Toast.error('請先初始化相機系統');
      return;
    }

    try {
      this.setState({ cameraStatus: '正在啟動相機...' });
      
      if (this.state.isMobile) {
        // 移動端：使用全屏相機
        this.showMobileCamera();
      } else {
        // Web端：直接啟動相機
        await this.state.cameraManager.startCamera('back');
        this.setState({ cameraStatus: '相機已啟動 (Web模式)' });
        
        Toast.success('相機啟動成功！');
        this.addTestResult('相機啟動', true, 'Web模式啟動成功');
      }
    } catch (error) {
      console.error('相機啟動失敗:', error);
      this.setState({ cameraStatus: '相機啟動失敗' });
      this.addTestResult('相機啟動', false, error.message);
      Toast.error('相機啟動失敗，請檢查權限');
    }
  }

  showMobileCamera() {
    if (!this.mobileCameraModal) {
      this.mobileCameraModal = new MobileCameraModal({
        visible: false,
        onClose: () => this.hideMobileCamera(),
        onPhotoTaken: (photoData) => this.handleMobilePhotoTaken(photoData),
        cameraManager: this.state.cameraManager,
        target: 'back'
      });
    }
    
    this.mobileCameraModal.updateOptions({ visible: true });
    this.setState({ showMobileCamera: true });
  }

  hideMobileCamera() {
    if (this.mobileCameraModal) {
      this.mobileCameraModal.updateOptions({ visible: false });
    }
    this.setState({ showMobileCamera: false });
  }

  handleMobilePhotoTaken(photoData) {
    this.setState({ 
      capturedImage: photoData.dataUrl,
      cameraStatus: '拍照成功'
    });
    
    this.addTestResult('移動端拍照', true, `圖片尺寸: ${photoData.width}x${photoData.height}`);
    Toast.success('拍照成功！');
  }

  async testCameraSwitch() {
    if (!this.state.cameraManager) {
      Toast.error('請先初始化相機系統');
      return;
    }

    try {
      if (!this.state.cameraManager.supportsCameraSwitch()) {
        throw new Error('設備不支持攝像頭切換');
      }

      const newFacingMode = await this.state.cameraManager.switchCamera();
      this.addTestResult('攝像頭切換', true, `切換到: ${newFacingMode}`);
      Toast.success(`攝像頭切換成功: ${newFacingMode}`);
      
    } catch (error) {
      console.error('攝像頭切換失敗:', error);
      this.addTestResult('攝像頭切換', false, error.message);
      Toast.error('攝像頭切換失敗');
    }
  }

  async runFullTest() {
    this.setState({ testResults: [] });
    
    try {
      // 1. 設備檢測
      await this.detectDevice();
      
      // 2. 相機初始化
      await this.initializeCamera();
      
      // 3. 相機功能測試
      if (this.state.cameraManager) {
        const capabilities = this.state.cameraManager.getCapabilities();
        
        this.addTestResult(
          '相機功能檢測',
          capabilities.hasCamera,
          `可用攝像頭: ${capabilities.availableCameras.length}個`
        );
        
        this.addTestResult(
          'facingMode支持',
          capabilities.supportsFacingMode,
          capabilities.supportsFacingMode ? '支持前後攝像頭切換' : '僅支持單一攝像頭'
        );
        
        this.addTestResult(
          '約束支持',
          capabilities.supportsConstraints,
          capabilities.supportsConstraints ? '支持高級相機約束' : '僅支持基本相機功能'
        );
      }
      
      Toast.success('完整測試完成！');
      
    } catch (error) {
      console.error('完整測試失敗:', error);
      this.addTestResult('完整測試', false, error.message);
      Toast.error('測試過程中出現錯誤');
    }
  }

  addTestResult(name, success, message, details = '') {
    const newResults = [...this.state.testResults];
    newResults.push({
      name,
      success,
      message,
      details,
      timestamp: new Date().toLocaleTimeString()
    });
    this.setState({ testResults: newResults });
  }

  clearCapturedImage() {
    this.setState({ capturedImage: null });
  }

  getStatusColor() {
    const status = this.state.cameraStatus;
    if (status.includes('成功') || status.includes('已啟動') || status.includes('已初始化')) {
      return 'var(--success-color)';
    } else if (status.includes('失敗') || status.includes('錯誤')) {
      return 'var(--error-color)';
    } else if (status.includes('正在')) {
      return 'var(--warning-color)';
    } else {
      return 'var(--text-color-secondary)';
    }
  }

  goBack() {
    router.navigate('/');
  }

  unmount() {
    // 停止相機
    if (this.state.cameraManager) {
      this.state.cameraManager.stopCamera();
    }
    
    // 銷毀移動端相機模態框
    if (this.mobileCameraModal) {
      this.mobileCameraModal.destroy();
      this.mobileCameraModal = null;
    }
  }
}

// 導出到全局
window.CameraTestPage = CameraTestPage;

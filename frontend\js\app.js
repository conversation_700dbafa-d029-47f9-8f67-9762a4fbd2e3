/**
 * 主應用程序 - 原生JavaScript版本
 * 負責應用初始化和路由配置
 */

class App {
  constructor() {
    this.isInitialized = false;
    this.cameraManager = null;
  }

  /**
   * 初始化應用
   */
  async init() {
    if (this.isInitialized) {
      return;
    }

    try {
      console.log('🚀 開始初始化名片OCR應用...');
      
      // 初始化相機管理器
      await this.initCameraManager();
      
      // 註冊路由
      this.registerRoutes();
      
      // 設置全局錯誤處理
      this.setupErrorHandling();
      
      // 設置PWA支持
      this.setupPWA();
      
      this.isInitialized = true;
      console.log('✅ 應用初始化完成');
      
    } catch (error) {
      console.error('❌ 應用初始化失敗:', error);
      this.showInitError(error);
    }
  }

  /**
   * 初始化相機管理器
   */
  async initCameraManager() {
    try {
      this.cameraManager = getCameraManager();
      
      // 預初始化相機管理器（不啟動相機）
      const envInfo = await getEnvironmentInfo();
      console.log('📱 設備環境信息:', {
        deviceType: envInfo.deviceType,
        hasCamera: envInfo.camera.hasCamera,
        supportsFacingMode: envInfo.camera.supportsFacingMode
      });
      
      // 將相機管理器設為全局可用
      window.globalCameraManager = this.cameraManager;
      
    } catch (error) {
      console.warn('⚠️ 相機管理器初始化失敗:', error);
      // 相機功能不可用時不阻止應用啟動
    }
  }

  /**
   * 註冊路由
   */
  registerRoutes() {
    // 註冊所有頁面路由
    router.register('/', HomePage);
    router.register('/scan', ScanUploadPage);
    router.register('/cards', CardManagerPage);
    router.register('/add-card', AddCardPage);
    router.register('/cards/:id', CardDetailPage);
    router.register('/camera-test', CameraTestPage);
    
    console.log('🛣️ 路由註冊完成');
  }

  /**
   * 設置全局錯誤處理
   */
  setupErrorHandling() {
    // 捕獲未處理的Promise錯誤
    window.addEventListener('unhandledrejection', (event) => {
      console.error('未處理的Promise錯誤:', event.reason);
      
      // 防止錯誤顯示在控制台
      event.preventDefault();
      
      // 顯示用戶友好的錯誤信息
      this.handleGlobalError(event.reason);
    });

    // 捕獲JavaScript運行時錯誤
    window.addEventListener('error', (event) => {
      console.error('JavaScript錯誤:', event.error);
      this.handleGlobalError(event.error);
    });

    console.log('🛡️ 全局錯誤處理設置完成');
  }

  /**
   * 處理全局錯誤
   */
  handleGlobalError(error) {
    // 避免重複顯示錯誤
    if (this.lastErrorTime && Date.now() - this.lastErrorTime < 1000) {
      return;
    }
    this.lastErrorTime = Date.now();

    let message = '應用運行時發生錯誤';
    
    if (error?.message) {
      // 根據錯誤類型提供更友好的信息
      if (error.message.includes('camera') || error.message.includes('相機')) {
        message = '相機功能出現問題，請檢查設備權限';
      } else if (error.message.includes('network') || error.message.includes('fetch')) {
        message = '網絡連接出現問題，請檢查網絡設置';
      } else if (error.message.includes('permission')) {
        message = '權限不足，請檢查瀏覽器設置';
      }
    }

    if (window.Toast) {
      Toast.error(message);
    } else {
      console.error('錯誤:', message);
    }
  }

  /**
   * 設置PWA支持
   */
  setupPWA() {
    // 檢查是否支持Service Worker
    if ('serviceWorker' in navigator) {
      // Service Worker註冊在index.html中處理
      console.log('📱 PWA支持已啟用');
    }

    // 處理安裝提示
    window.addEventListener('beforeinstallprompt', (event) => {
      // 阻止默認的安裝提示
      event.preventDefault();
      
      // 保存事件以便後續使用
      window.deferredPrompt = event;
      
      console.log('📲 PWA安裝提示已準備');
    });

    // 處理應用安裝
    window.addEventListener('appinstalled', () => {
      console.log('✅ PWA已安裝');
      window.deferredPrompt = null;
    });
  }

  /**
   * 顯示初始化錯誤
   */
  showInitError(error) {
    const appContainer = document.getElementById('app');
    if (appContainer) {
      appContainer.innerHTML = `
        <div class="App" style="display: flex; align-items: center; justify-content: center;">
          <div class="card" style="max-width: 400px; text-align: center;">
            <div class="card-body">
              <h2 style="margin-bottom: 16px; color: var(--error-color);">
                <i class="fas fa-exclamation-triangle"></i> 應用初始化失敗
              </h2>
              <p style="margin-bottom: 24px; color: var(--text-color-secondary);">
                ${error.message || '未知錯誤，請刷新頁面重試'}
              </p>
              <button class="btn btn-primary btn-large" onclick="window.location.reload()">
                <i class="fas fa-redo"></i> 重新載入
              </button>
            </div>
          </div>
        </div>
      `;
    }
  }

  /**
   * 獲取應用狀態
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      cameraManager: this.cameraManager?.getStatus(),
      currentRoute: router.getCurrentRoute()
    };
  }

  /**
   * 重啟應用
   */
  async restart() {
    this.isInitialized = false;
    await this.init();
  }
}

// 創建全局應用實例
const app = new App();

// 導出到全局
window.App = app;

// 應用工具函數
window.AppUtils = {
  /**
   * 顯示PWA安裝提示
   */
  showInstallPrompt() {
    if (window.deferredPrompt) {
      window.deferredPrompt.prompt();
      
      window.deferredPrompt.userChoice.then((choiceResult) => {
        if (choiceResult.outcome === 'accepted') {
          console.log('用戶接受了PWA安裝');
        } else {
          console.log('用戶拒絕了PWA安裝');
        }
        window.deferredPrompt = null;
      });
    } else {
      if (window.Toast) {
        Toast.show({ content: '此應用已安裝或瀏覽器不支持安裝' });
      }
    }
  },

  /**
   * 檢查是否為PWA模式
   */
  isPWA() {
    return window.matchMedia && window.matchMedia('(display-mode: standalone)').matches;
  },

  /**
   * 獲取應用版本信息
   */
  getVersion() {
    return {
      app: '1.0.0',
      build: new Date().toISOString().split('T')[0]
    };
  },

  /**
   * 清除應用緩存
   */
  async clearCache() {
    try {
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        await Promise.all(
          cacheNames.map(cacheName => caches.delete(cacheName))
        );
      }
      
      // 清除localStorage
      localStorage.clear();
      
      if (window.Toast) {
        Toast.success('緩存清除成功');
      }
      
      // 重新載入頁面
      setTimeout(() => {
        window.location.reload();
      }, 1000);
      
    } catch (error) {
      console.error('清除緩存失敗:', error);
      if (window.Toast) {
        Toast.error('清除緩存失敗');
      }
    }
  }
};

console.log('📦 應用模塊載入完成');

/**
 * 首頁組件 - 原生JavaScript版本
 */

class HomePage extends Component {
  constructor(props = {}) {
    super(props);
    this.state = {
      loading: false
    };
  }

  render() {
    return `
      <div class="App">
        <div class="card" style="width: 90%; max-width: 400px; margin: 0 auto; box-shadow: var(--shadow-light);">
          <div class="card-body" style="text-align: center;">
            <h2 style="margin-bottom: 32px; color: var(--text-color);">
              <i class="fas fa-id-card" style="margin-right: 8px; color: var(--primary-color);"></i>
              名片 OCR 應用
            </h2>
            
            <div class="space space-vertical" style="width: 100%;">
              <button 
                class="btn btn-primary btn-large btn-block" 
                onclick="this.handleNavigation('/scan')"
                style="font-size: 18px;"
              >
                <i class="fas fa-camera icon"></i>
                開始掃描 / 上傳
              </button>
              
              <button 
                class="btn btn-default btn-large btn-block" 
                onclick="this.handleNavigation('/cards')"
                style="font-size: 18px;"
              >
                <i class="fas fa-address-book icon"></i>
                名片管理
              </button>
              
              <button 
                class="btn btn-warning btn-large btn-block" 
                onclick="this.handleNavigation('/camera-test')"
                style="font-size: 18px;"
              >
                <i class="fas fa-video icon"></i>
                相機測試
              </button>
            </div>
            
            <div style="margin-top: 24px; padding-top: 16px; border-top: 1px solid var(--border-color);">
              <p style="color: var(--text-color-secondary); font-size: 14px; margin: 0;">
                <i class="fas fa-info-circle" style="margin-right: 4px;"></i>
                支持拍照掃描和手動輸入名片信息
              </p>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  mount() {
    // 綁定導航事件
    this.bindNavigationEvents();
    
    // 檢查設備兼容性
    this.checkDeviceCompatibility();
  }

  /**
   * 綁定導航事件
   */
  bindNavigationEvents() {
    const buttons = document.querySelectorAll('button[onclick*="handleNavigation"]');
    buttons.forEach(button => {
      button.onclick = (event) => {
        event.preventDefault();
        const path = this.extractPathFromOnclick(button.getAttribute('onclick'));
        if (path) {
          this.handleNavigation(path);
        }
      };
    });
  }

  /**
   * 從onclick屬性中提取路徑
   */
  extractPathFromOnclick(onclickStr) {
    const match = onclickStr.match(/handleNavigation\(['"]([^'"]+)['"]\)/);
    return match ? match[1] : null;
  }

  /**
   * 處理導航
   */
  handleNavigation(path) {
    if (this.state.loading) return;
    
    this.setState({ loading: true });
    
    // 添加點擊反饋
    const button = event?.target?.closest('button');
    if (button) {
      button.style.transform = 'scale(0.98)';
      setTimeout(() => {
        button.style.transform = '';
      }, 150);
    }
    
    // 延遲導航以顯示點擊效果
    setTimeout(() => {
      router.navigate(path);
      this.setState({ loading: false });
    }, 200);
  }

  /**
   * 檢查設備兼容性
   */
  async checkDeviceCompatibility() {
    try {
      const envInfo = await getEnvironmentInfo();
      
      // 檢查相機支持
      if (!envInfo.camera.hasCamera) {
        this.showCompatibilityWarning('相機功能不可用，部分功能可能受限');
      }
      
      // 檢查現代瀏覽器特性
      if (!window.fetch || !window.Promise) {
        this.showCompatibilityWarning('瀏覽器版本過舊，建議升級瀏覽器以獲得最佳體驗');
      }
      
      // 移動端特殊提示
      if (envInfo.isMobile && !envInfo.camera.supportsFacingMode) {
        console.warn('設備不支持攝像頭切換功能');
      }
      
    } catch (error) {
      console.error('設備兼容性檢查失敗:', error);
    }
  }

  /**
   * 顯示兼容性警告
   */
  showCompatibilityWarning(message) {
    // 創建警告提示
    const warning = document.createElement('div');
    warning.style.cssText = `
      position: fixed;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      background: var(--warning-color);
      color: white;
      padding: 12px 20px;
      border-radius: var(--border-radius);
      font-size: 14px;
      z-index: 1000;
      max-width: 90%;
      text-align: center;
      box-shadow: var(--shadow-medium);
    `;
    warning.innerHTML = `
      <i class="fas fa-exclamation-triangle" style="margin-right: 8px;"></i>
      ${message}
    `;
    
    document.body.appendChild(warning);
    
    // 5秒後自動移除
    setTimeout(() => {
      if (warning.parentNode) {
        warning.parentNode.removeChild(warning);
      }
    }, 5000);
  }

  unmount() {
    // 清理事件監聽器
    const buttons = document.querySelectorAll('button[onclick*="handleNavigation"]');
    buttons.forEach(button => {
      button.onclick = null;
    });
  }
}

// 導出到全局
window.HomePage = HomePage;

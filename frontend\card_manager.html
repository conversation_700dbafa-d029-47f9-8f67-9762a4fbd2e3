<!DOCTYPE html>
<html lang="zh-Hant">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>名片管理</title>
  <style>
    body {
      font-family: 'Noto Sans TC', 'Microsoft JhengHei', <PERSON>l, sans-serif;
      background: #f5f5f5;
      margin: 0;
      padding: 0;
      color: #222;
    }
    .card-manager-page {
      max-width: 600px;
      margin: 0 auto;
      background: #fff;
      min-height: 100vh;
      box-shadow: 0 2px 8px rgba(0,0,0,0.04);
    }
    .navbar {
      background: #1677ff;
      color: #fff;
      padding: 16px;
      font-size: 20px;
      font-weight: bold;
      text-align: center;
      position: sticky;
      top: 0;
      z-index: 10;
    }
    .content {
      padding: 16px;
    }
    .search-bar {
      width: 100%;
      padding: 10px 12px;
      border: 1px solid #d9d9d9;
      border-radius: 6px;
      font-size: 16px;
      margin-bottom: 16px;
      box-sizing: border-box;
      background: #fafcff;
    }
    .card-list {
      margin-bottom: 16px;
    }
    .card-item {
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.06);
      margin-bottom: 12px;
      padding: 16px;
      cursor: pointer;
      transition: box-shadow 0.2s;
      border: 1px solid #f0f0f0;
    }
    .card-item:hover {
      box-shadow: 0 4px 16px rgba(22,119,255,0.08);
      border: 1px solid #1677ff;
    }
    .card-header {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 8px;
    }
    .card-name {
      font-size: 17px;
      font-weight: bold;
      color: #262626;
    }
    .card-company {
      font-size: 15px;
      color: #8c8c8c;
    }
    .card-position {
      display: inline-block;
      background: #e6f4ff;
      color: #1677ff;
      border-radius: 4px;
      padding: 2px 8px;
      font-size: 13px;
      margin-left: 8px;
    }
    .card-contact {
      font-size: 14px;
      color: #444;
      margin-bottom: 4px;
      display: flex;
      align-items: center;
      gap: 6px;
    }
    .card-extra {
      font-size: 13px;
      color: #888;
      margin-top: 6px;
    }
    .card-actions {
      margin-top: 10px;
      display: flex;
      gap: 10px;
    }
    .card-actions button {
      flex: 1;
      padding: 7px 0;
      border-radius: 6px;
      border: none;
      font-size: 15px;
      font-weight: bold;
      cursor: pointer;
      background: #fff;
      color: #1677ff;
      border: 1px solid #1677ff;
      transition: background 0.2s, color 0.2s;
    }
    .card-actions button.delete {
      color: #ff4d4f;
      border-color: #ff4d4f;
    }
    .card-actions button.delete:hover {
      background: #fff1f0;
    }
    .card-actions button.export {
      color: #52c41a;
      border-color: #52c41a;
    }
    .card-actions button.export:hover {
      background: #f6ffed;
    }
    .empty {
      text-align: center;
      color: #bbb;
      font-size: 16px;
      margin: 40px 0;
    }
    .export-bar {
      display: flex;
      gap: 10px;
      margin-bottom: 18px;
    }
    .export-bar button {
      flex: 1;
      padding: 10px 0;
      border-radius: 6px;
      border: none;
      font-size: 15px;
      font-weight: bold;
      cursor: pointer;
      background: #fff;
      color: #52c41a;
      border: 1px solid #52c41a;
      transition: background 0.2s, color 0.2s;
    }
    .export-bar button:hover {
      background: #f6ffed;
    }
    .add-btn {
      width: 100%;
      padding: 14px 0;
      font-size: 18px;
      font-weight: bold;
      background: #1677ff;
      color: #fff;
      border: none;
      border-radius: 8px;
      margin-top: 12px;
      cursor: pointer;
      transition: background 0.2s;
    }
    .add-btn:hover {
      background: #0958d9;
    }
    /* Toast/Loading/Modal */
    .toast {
      position: fixed;
      left: 50%;
      top: 40%;
      transform: translate(-50%, -50%);
      background: rgba(34,34,34,0.95);
      color: #fff;
      padding: 16px 28px;
      border-radius: 8px;
      font-size: 16px;
      z-index: 9999;
      box-shadow: 0 2px 8px rgba(0,0,0,0.18);
      animation: fadeIn 0.2s;
    }
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    .loading {
      position: fixed;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      background: rgba(255,255,255,0.9);
      color: #1677ff;
      padding: 18px 32px;
      border-radius: 8px;
      font-size: 18px;
      z-index: 9999;
      display: flex;
      align-items: center;
      gap: 10px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.12);
    }
    .modal-mask {
      position: fixed;
      left: 0; top: 0; right: 0; bottom: 0;
      background: rgba(0,0,0,0.35);
      z-index: 9998;
    }
    .modal {
      position: fixed;
      left: 50%; top: 50%;
      transform: translate(-50%, -50%);
      background: #fff;
      border-radius: 10px;
      padding: 24px 18px 18px 18px;
      min-width: 260px;
      z-index: 9999;
      box-shadow: 0 2px 16px rgba(0,0,0,0.18);
      text-align: center;
    }
    .modal .modal-title {
      font-size: 17px;
      font-weight: bold;
      margin-bottom: 12px;
    }
    .modal .modal-actions {
      margin-top: 18px;
      display: flex;
      gap: 12px;
      justify-content: center;
    }
    @media (max-width: 700px) {
      .card-manager-page { max-width: 100vw; }
      .card-item { padding: 10px; }
      .content { padding: 8px; }
    }
  </style>
</head>
<body>
<div class="card-manager-page">
  <div class="navbar">名片管理</div>
  <div class="content">
    <input class="search-bar" id="search-bar" placeholder="搜尋姓名、公司、職位、電話、Email..." />
    <div class="export-bar">
      <button onclick="exportCards('csv')">匯出CSV</button>
      <button onclick="exportCards('excel')">匯出Excel</button>
      <button onclick="exportCards('vcard')">匯出vCard</button>
    </div>
    <div class="card-list" id="card-list"></div>
    <div class="empty" id="empty-list" style="display:none;">暫無名片資料</div>
    <button class="add-btn" onclick="window.location.href='add_card.html'">＋ 新增名片</button>
  </div>
</div>
<!-- Toast/Loading/Modal 元素 -->
<div id="toast" class="toast" style="display:none;"></div>
<div id="loading" class="loading" style="display:none;"><span>⏳</span> 請稍候...</div>
<div id="modal-mask" class="modal-mask" style="display:none;"></div>
<div id="modal" class="modal" style="display:none;">
  <div class="modal-title" id="modal-title"></div>
  <div id="modal-content"></div>
  <div class="modal-actions" id="modal-actions"></div>
</div>
<script>
// Toast/Loading/Modal 工具
function showToast(msg, ms=1800) {
  const toast = document.getElementById('toast');
  toast.textContent = msg;
  toast.style.display = 'block';
  setTimeout(() => { toast.style.display = 'none'; }, ms);
}
function showLoading(show) {
  document.getElementById('loading').style.display = show ? 'flex' : 'none';
}
function showModal(title, content, actionsHtml) {
  document.getElementById('modal-title').textContent = title;
  document.getElementById('modal-content').innerHTML = content;
  document.getElementById('modal-actions').innerHTML = actionsHtml;
  document.getElementById('modal-mask').style.display = 'block';
  document.getElementById('modal').style.display = 'block';
}
function closeModal() {
  document.getElementById('modal-mask').style.display = 'none';
  document.getElementById('modal').style.display = 'none';
}

// 名片列表渲染
let allCards = [];
function renderCards(cards) {
  const list = document.getElementById('card-list');
  list.innerHTML = '';
  if (!cards.length) {
    document.getElementById('empty-list').style.display = '';
    return;
  }
  document.getElementById('empty-list').style.display = 'none';
  cards.forEach(card => {
    const div = document.createElement('div');
    div.className = 'card-item';
    div.onclick = () => { window.location.href = `card_detail.html?id=${card.id}`; };
    div.innerHTML = `
      <div class="card-header">
        <span class="card-name">${card.name || '未知姓名'}</span>
        ${card.company_name ? `<span class="card-company">${card.company_name}</span>` : ''}
        ${card.position ? `<span class="card-position">${card.position}</span>` : ''}
      </div>
      <div class="card-contact">${card.mobile_phone ? '📱 ' + card.mobile_phone : ''}</div>
      <div class="card-contact">${card.office_phone ? '☎️ ' + card.office_phone : ''}</div>
      <div class="card-contact">${card.email ? '✉️ ' + card.email : ''}</div>
      <div class="card-contact">${card.company_address_1 ? '🏢 ' + card.company_address_1 : ''}</div>
      <div class="card-extra">${card.line_id ? 'Line ID: ' + card.line_id : ''}</div>
      <div class="card-extra">${card.notes ? '備註: ' + card.notes : ''}</div>
      <div class="card-actions">
        <button class="delete" onclick="event.stopPropagation();deleteCard(${card.id})">刪除</button>
      </div>
    `;
    list.appendChild(div);
  });
}

// 載入名片
function loadCards() {
  showLoading(true);
  fetch('/api/v1/cards/')
    .then(r => r.json())
    .then(data => {
      allCards = data || [];
      renderCards(allCards);
    })
    .catch(() => {
      showToast('載入名片失敗');
    })
    .finally(() => {
      showLoading(false);
    });
}

// 搜尋功能
const searchBar = document.getElementById('search-bar');
searchBar.oninput = function() {
  const kw = searchBar.value.trim().toLowerCase();
  if (!kw) {
    renderCards(allCards);
    return;
  }
  const filtered = allCards.filter(card =>
    (card.name && card.name.toLowerCase().includes(kw)) ||
    (card.company_name && card.company_name.toLowerCase().includes(kw)) ||
    (card.position && card.position.toLowerCase().includes(kw)) ||
    (card.mobile_phone && card.mobile_phone.includes(kw)) ||
    (card.office_phone && card.office_phone.includes(kw)) ||
    (card.email && card.email.toLowerCase().includes(kw))
  );
  renderCards(filtered);
};

// 刪除名片
function deleteCard(id) {
  showModal('刪除確認', '確定要刪除這張名片嗎？', `
    <button onclick="confirmDelete(${id})" style="background:#ff4d4f;color:#fff;">確定</button>
    <button onclick="closeModal()">取消</button>
  `);
}
function confirmDelete(id) {
  showLoading(true);
  fetch(`/api/v1/cards/${id}`, { method: 'DELETE' })
    .then(r => r.json())
    .then(res => {
      if (res.success) {
        showToast('刪除成功');
        loadCards();
      } else {
        showToast('刪除失敗');
      }
    })
    .catch(() => {
      showToast('刪除失敗');
    })
    .finally(() => {
      showLoading(false);
      closeModal();
    });
}

// 匯出功能
function exportCards(format) {
  showLoading(true);
  fetch(`/api/v1/cards/export/download?format=${format}`)
    .then(r => r.blob())
    .then(blob => {
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      const ext = format === 'excel' ? 'xlsx' : (format === 'vcard' ? 'vcf' : 'csv');
      a.download = `cards.${ext}`;
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
      showToast(format.toUpperCase() + '匯出成功');
    })
    .catch(() => {
      showToast('匯出失敗');
    })
    .finally(() => {
      showLoading(false);
    });
}

// 初始化
loadCards();
</script>
</body>
</html> 
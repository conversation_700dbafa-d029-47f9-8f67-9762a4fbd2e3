/**
 * 名片管理頁面 - 原生JavaScript版本
 */

class CardManagerPage extends Component {
  constructor(props = {}) {
    super(props);
    this.state = {
      loading: true,
      cards: [],
      searchText: '',
      filteredCards: []
    };
  }

  render() {
    return `
      <div class="App">
        <!-- 導航欄 -->
        <div class="navbar">
          <button class="navbar-back" onclick="this.goBack()">
            <i class="fas fa-arrow-left"></i>
          </button>
          <div class="navbar-title">名片管理</div>
          <div class="navbar-right"></div>
        </div>

        <div class="content">
          <!-- 搜索欄 -->
          <div class="search-bar">
            <i class="fas fa-search search-icon"></i>
            <input 
              type="text" 
              class="search-input" 
              placeholder="搜索名片（姓名、公司、電話、郵箱）"
              value="${this.state.searchText}"
              oninput="this.handleSearch(this.value)"
            >
          </div>

          <!-- 操作按鈕 -->
          <div class="card mb-md">
            <div class="card-body">
              <div class="space space-vertical">
                <div class="space">
                  <button 
                    class="btn btn-primary btn-large" 
                    style="flex: 1;"
                    onclick="this.navigateToAddCard()"
                  >
                    <i class="fas fa-plus icon"></i>
                    手動新增
                  </button>
                  <button 
                    class="btn btn-default btn-large" 
                    style="flex: 1;"
                    onclick="this.navigateToScan()"
                  >
                    <i class="fas fa-camera icon"></i>
                    OCR掃描
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 名片列表 -->
          ${this.renderCardsList()}
        </div>
      </div>
    `;
  }

  renderCardsList() {
    if (this.state.loading) {
      return `
        <div class="card">
          <div class="card-body text-center">
            <div class="loading-spinner" style="margin: 40px auto;"></div>
            <p style="color: var(--text-color-secondary);">載入中...</p>
          </div>
        </div>
      `;
    }

    const cards = this.state.filteredCards.length > 0 ? this.state.filteredCards : this.state.cards;

    if (cards.length === 0) {
      return `
        <div class="card">
          <div class="card-body text-center">
            <i class="fas fa-address-book" style="font-size: 48px; color: var(--text-color-secondary); margin-bottom: 16px;"></i>
            <h3 style="margin-bottom: 8px; color: var(--text-color);">暫無名片</h3>
            <p style="color: var(--text-color-secondary); margin-bottom: 24px;">
              ${this.state.searchText ? '未找到匹配的名片' : '開始添加您的第一張名片吧'}
            </p>
            ${!this.state.searchText ? `
              <div class="space">
                <button class="btn btn-primary" onclick="this.navigateToAddCard()">
                  <i class="fas fa-plus icon"></i>
                  手動新增
                </button>
                <button class="btn btn-default" onclick="this.navigateToScan()">
                  <i class="fas fa-camera icon"></i>
                  掃描名片
                </button>
              </div>
            ` : ''}
          </div>
        </div>
      `;
    }

    return `
      <div class="card-grid">
        ${cards.map(card => this.renderCardItem(card)).join('')}
      </div>
    `;
  }

  renderCardItem(card) {
    return `
      <div class="card card-item" onclick="this.viewCardDetail('${card.id}')">
        <div class="card-body">
          <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 12px;">
            <h4 style="margin: 0; color: var(--text-color); font-size: 18px;">
              ${card.name || '未知姓名'}
            </h4>
            <button 
              class="btn btn-danger btn-small" 
              onclick="event.stopPropagation(); this.deleteCard('${card.id}')"
              style="padding: 4px 8px;"
            >
              <i class="fas fa-trash"></i>
            </button>
          </div>
          
          ${card.company_name ? `
            <p style="margin: 4px 0; color: var(--text-color-secondary); font-size: 14px;">
              <i class="fas fa-building" style="width: 16px; margin-right: 8px;"></i>
              ${card.company_name}
            </p>
          ` : ''}
          
          ${card.position ? `
            <p style="margin: 4px 0; color: var(--text-color-secondary); font-size: 14px;">
              <i class="fas fa-briefcase" style="width: 16px; margin-right: 8px;"></i>
              ${card.position}
            </p>
          ` : ''}
          
          ${card.mobile_phone ? `
            <p style="margin: 4px 0; color: var(--text-color-secondary); font-size: 14px;">
              <i class="fas fa-mobile-alt" style="width: 16px; margin-right: 8px;"></i>
              ${card.mobile_phone}
            </p>
          ` : ''}
          
          ${card.email ? `
            <p style="margin: 4px 0; color: var(--text-color-secondary); font-size: 14px;">
              <i class="fas fa-envelope" style="width: 16px; margin-right: 8px;"></i>
              ${card.email}
            </p>
          ` : ''}
          
          <div style="margin-top: 12px; padding-top: 8px; border-top: 1px solid var(--border-color); font-size: 12px; color: var(--text-color-disabled);">
            <i class="fas fa-clock" style="margin-right: 4px;"></i>
            ${this.formatDate(card.created_at)}
          </div>
        </div>
      </div>
    `;
  }

  async mount() {
    await this.loadCards();
    this.bindEvents();
  }

  bindEvents() {
    // 綁定搜索輸入事件
    const searchInput = document.querySelector('.search-input');
    if (searchInput) {
      searchInput.oninput = (event) => {
        this.handleSearch(event.target.value);
      };
    }

    // 綁定其他onclick事件
    const buttons = document.querySelectorAll('button[onclick], div[onclick]');
    buttons.forEach(element => {
      const onclickStr = element.getAttribute('onclick');
      if (onclickStr) {
        element.onclick = (event) => {
          event.preventDefault();
          this.executeOnclickFunction(onclickStr, event);
        };
      }
    });
  }

  executeOnclickFunction(funcStr, event) {
    try {
      const func = new Function('event', `return (${funcStr}).call(this, event)`);
      func.call(this, event);
    } catch (error) {
      console.error('執行onclick函數失敗:', error);
    }
  }

  async loadCards() {
    try {
      this.setState({ loading: true });
      const response = await cardsApi.getAll();
      
      if (response && Array.isArray(response)) {
        this.setState({ 
          cards: response,
          filteredCards: [],
          loading: false 
        });
      } else {
        this.setState({ 
          cards: [],
          filteredCards: [],
          loading: false 
        });
      }
    } catch (error) {
      console.error('載入名片失敗:', error);
      this.setState({ loading: false });
      errorHandler.showError(error);
    }
  }

  handleSearch(searchText) {
    this.setState({ searchText });
    
    if (!searchText.trim()) {
      this.setState({ filteredCards: [] });
      return;
    }

    const filtered = this.state.cards.filter(card => {
      const searchLower = searchText.toLowerCase();
      return (
        (card.name && card.name.toLowerCase().includes(searchLower)) ||
        (card.company_name && card.company_name.toLowerCase().includes(searchLower)) ||
        (card.position && card.position.toLowerCase().includes(searchLower)) ||
        (card.mobile_phone && card.mobile_phone.includes(searchText)) ||
        (card.office_phone && card.office_phone.includes(searchText)) ||
        (card.email && card.email.toLowerCase().includes(searchLower)) ||
        (card.line_id && card.line_id.toLowerCase().includes(searchLower))
      );
    });

    this.setState({ filteredCards: filtered });
  }

  goBack() {
    router.navigate('/');
  }

  navigateToAddCard() {
    router.navigate('/add-card');
  }

  navigateToScan() {
    router.navigate('/scan');
  }

  viewCardDetail(cardId) {
    router.navigate(`/cards/${cardId}`);
  }

  async deleteCard(cardId) {
    const confirmed = await this.showConfirmDialog(
      '確認刪除',
      '確定要刪除這張名片嗎？此操作無法撤銷。'
    );

    if (!confirmed) return;

    try {
      await cardsApi.delete(cardId);
      Toast.success('名片刪除成功');
      
      // 從列表中移除已刪除的名片
      const newCards = this.state.cards.filter(card => card.id !== cardId);
      const newFilteredCards = this.state.filteredCards.filter(card => card.id !== cardId);
      
      this.setState({ 
        cards: newCards,
        filteredCards: newFilteredCards
      });
    } catch (error) {
      console.error('刪除名片失敗:', error);
      errorHandler.showError(error);
    }
  }

  showConfirmDialog(title, content) {
    return new Promise((resolve) => {
      Dialog.confirm({
        title,
        content,
        onConfirm: () => resolve(true),
        onCancel: () => resolve(false)
      });
    });
  }

  formatDate(dateString) {
    if (!dateString) return '';
    
    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffTime = Math.abs(now - date);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      
      if (diffDays === 1) {
        return '今天';
      } else if (diffDays === 2) {
        return '昨天';
      } else if (diffDays <= 7) {
        return `${diffDays - 1}天前`;
      } else {
        return date.toLocaleDateString('zh-TW', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit'
        });
      }
    } catch (error) {
      return dateString;
    }
  }

  unmount() {
    // 清理事件監聽器
  }
}

// 導出到全局
window.CardManagerPage = CardManagerPage;

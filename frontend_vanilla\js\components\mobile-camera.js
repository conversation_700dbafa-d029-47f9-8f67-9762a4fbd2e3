/**
 * 移動端相機組件 - 原生JavaScript版本
 * 提供全屏相機拍照功能
 */

class MobileCameraModal {
  constructor(options = {}) {
    this.options = {
      visible: false,
      onClose: null,
      onPhotoTaken: null,
      cameraManager: null,
      target: 'back',
      ...options
    };
    
    this.element = null;
    this.videoElement = null;
    this.canvasElement = null;
    this.isReady = false;
    this.supportsCameraSwitch = false;
    this.currentFacingMode = 'environment';
    this.isCapturing = false;
    this.showGrid = false;
    this.focusPoint = null;
    this.focusTimeout = null;
    
    this.init();
  }

  /**
   * 初始化組件
   */
  init() {
    this.createElement();
    this.bindEvents();
    
    if (this.options.visible) {
      this.show();
    }
  }

  /**
   * 創建DOM元素
   */
  createElement() {
    this.element = document.createElement('div');
    this.element.className = 'mobile-camera-modal';
    this.element.style.display = 'none';
    
    this.element.innerHTML = `
      <div class="camera-container">
        <!-- 視頻預覽 -->
        <video
          class="camera-video"
          autoplay
          playsinline
          muted
        ></video>
        
        <!-- 網格線輔助 -->
        <div class="camera-grid ${!this.showGrid ? 'hidden' : ''}"></div>
        
        <!-- 隱藏的畫布用於拍照 -->
        <canvas style="display: none;"></canvas>
        
        <!-- 對焦指示器 -->
        <div class="focus-indicator"></div>
        
        <!-- 相機未準備就緒時的加載提示 -->
        <div class="camera-loading">
          <div class="loading-spinner"></div>
        </div>
        
        <!-- 拍攝指引 -->
        <div class="camera-guides">
          <div class="guide-corner top-left"></div>
          <div class="guide-corner top-right"></div>
          <div class="guide-corner bottom-left"></div>
          <div class="guide-corner bottom-right"></div>
        </div>
        
        <!-- 拍攝範圍提示 -->
        <div class="capture-hint">
          <div class="hint-text">將名片置於框內</div>
          <div class="hint-subtext">確保文字清晰可見</div>
        </div>
        
        <!-- 控制按鈕 -->
        <div class="camera-controls">
          <div class="controls-top">
            <button class="control-button close-button" data-action="close">
              <i class="fas fa-times"></i>
            </button>
            <button class="control-button grid-button" data-action="toggle-grid">
              <i class="fas fa-th"></i>
            </button>
            <button class="control-button switch-button" data-action="switch-camera" style="display: none;">
              <i class="fas fa-sync-alt"></i>
            </button>
          </div>
          
          <div class="controls-bottom">
            <div class="capture-area">
              <button class="capture-button" data-action="capture" disabled>
                <i class="fas fa-camera"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    `;
    
    // 獲取關鍵元素引用
    this.videoElement = this.element.querySelector('.camera-video');
    this.canvasElement = this.element.querySelector('canvas');
    this.loadingElement = this.element.querySelector('.camera-loading');
    this.focusIndicator = this.element.querySelector('.focus-indicator');
    this.gridElement = this.element.querySelector('.camera-grid');
    this.captureButton = this.element.querySelector('[data-action="capture"]');
    this.switchButton = this.element.querySelector('[data-action="switch-camera"]');
    
    // 添加到頁面
    document.body.appendChild(this.element);
  }

  /**
   * 綁定事件
   */
  bindEvents() {
    // 控制按鈕事件
    this.element.addEventListener('click', (event) => {
      const action = event.target.closest('[data-action]')?.getAttribute('data-action');
      
      switch (action) {
        case 'close':
          this.hide();
          break;
        case 'capture':
          this.handleCapture();
          break;
        case 'switch-camera':
          this.handleSwitchCamera();
          break;
        case 'toggle-grid':
          this.toggleGrid();
          break;
      }
    });

    // 視頻點擊對焦
    this.videoElement.addEventListener('click', (event) => {
      this.handleFocus(event);
    });

    // 防止背景滾動
    this.element.addEventListener('touchmove', (event) => {
      event.preventDefault();
    }, { passive: false });

    // 相機管理器事件
    if (this.options.cameraManager) {
      this.setupCameraEvents();
    }
  }

  /**
   * 設置相機事件
   */
  setupCameraEvents() {
    const manager = this.options.cameraManager;
    
    manager.setCallbacks({
      cameraStart: (data) => this.handleCameraStart(data),
      cameraError: (error) => this.handleCameraError(error),
      photoTaken: (data) => this.handlePhotoTaken(data),
      photoError: (error) => this.handlePhotoError(error),
      cameraSwitch: (data) => this.handleCameraSwitch(data)
    });
  }

  /**
   * 顯示相機模態框
   */
  async show() {
    this.element.style.display = 'flex';
    this.options.visible = true;
    
    // 阻止背景滾動
    document.body.style.overflow = 'hidden';
    
    try {
      await this.startCamera();
    } catch (error) {
      console.error('啟動相機失敗:', error);
      this.handleCameraError(error);
    }
  }

  /**
   * 隱藏相機模態框
   */
  hide() {
    this.element.style.display = 'none';
    this.options.visible = false;
    
    // 恢復背景滾動
    document.body.style.overflow = '';
    
    // 停止相機
    this.stopCamera();
    
    // 調用關閉回調
    if (this.options.onClose) {
      this.options.onClose();
    }
  }

  /**
   * 啟動相機
   */
  async startCamera() {
    if (!this.options.cameraManager) {
      throw new Error('相機管理器未設置');
    }

    this.isReady = false;
    this.showLoading(true);
    
    try {
      // 設置視頻和畫布元素
      this.options.cameraManager.setElements(this.videoElement, this.canvasElement);
      
      // 啟動相機
      await this.options.cameraManager.startCamera(this.options.target);
      
      // 檢查是否支持攝像頭切換
      this.supportsCameraSwitch = this.options.cameraManager.supportsCameraSwitch();
      this.updateSwitchButton();
      
    } catch (error) {
      this.showLoading(false);
      throw error;
    }
  }

  /**
   * 停止相機
   */
  stopCamera() {
    if (this.options.cameraManager) {
      this.options.cameraManager.stopCamera();
    }
    this.isReady = false;
    this.showLoading(false);
  }

  /**
   * 處理拍照
   */
  async handleCapture() {
    if (!this.isReady || this.isCapturing || !this.options.cameraManager) {
      return;
    }

    this.isCapturing = true;
    this.captureButton.disabled = true;

    try {
      const photoData = await this.options.cameraManager.takePhoto();
      
      if (this.options.onPhotoTaken) {
        this.options.onPhotoTaken(photoData);
      }
      
      // 拍照成功後關閉模態框
      this.hide();
      
    } catch (error) {
      console.error('拍照失敗:', error);
      this.handlePhotoError(error);
    } finally {
      this.isCapturing = false;
      this.captureButton.disabled = false;
    }
  }

  /**
   * 處理攝像頭切換
   */
  async handleSwitchCamera() {
    if (!this.supportsCameraSwitch || !this.options.cameraManager) {
      return;
    }

    try {
      this.showLoading(true);
      await this.options.cameraManager.switchCamera();
    } catch (error) {
      console.error('攝像頭切換失敗:', error);
      if (window.Toast) {
        Toast.error('攝像頭切換失敗');
      }
    } finally {
      this.showLoading(false);
    }
  }

  /**
   * 切換網格線
   */
  toggleGrid() {
    this.showGrid = !this.showGrid;
    this.gridElement.classList.toggle('hidden', !this.showGrid);
  }

  /**
   * 處理對焦
   */
  handleFocus(event) {
    const rect = this.videoElement.getBoundingClientRect();
    const x = ((event.clientX - rect.left) / rect.width) * 100;
    const y = ((event.clientY - rect.top) / rect.height) * 100;

    // 顯示對焦指示器
    this.focusPoint = { x, y };
    this.focusIndicator.style.left = `${x}%`;
    this.focusIndicator.style.top = `${y}%`;
    this.focusIndicator.classList.add('active');

    // 清除之前的定時器
    if (this.focusTimeout) {
      clearTimeout(this.focusTimeout);
    }

    // 1秒後隱藏對焦指示器
    this.focusTimeout = setTimeout(() => {
      this.focusIndicator.classList.remove('active');
      this.focusPoint = null;
    }, 1000);
  }

  /**
   * 相機啟動成功回調
   */
  handleCameraStart(data) {
    this.isReady = true;
    this.showLoading(false);
    this.captureButton.disabled = false;
    
    if (data.facingMode) {
      this.currentFacingMode = data.facingMode;
    }
  }

  /**
   * 相機錯誤回調
   */
  handleCameraError(error) {
    this.showLoading(false);
    
    let message = '相機啟動失敗';
    if (error.name === 'PermissionError') {
      message = '相機權限被拒絕，請允許相機權限後重試';
    } else if (error.name === 'DeviceError') {
      message = '未找到相機設備';
    } else if (error.name === 'OccupiedError') {
      message = '相機被其他應用占用';
    }
    
    if (window.Toast) {
      Toast.error(message);
    } else {
      alert(message);
    }
  }

  /**
   * 拍照成功回調
   */
  handlePhotoTaken(data) {
    console.log('拍照成功:', data);
  }

  /**
   * 拍照錯誤回調
   */
  handlePhotoError(error) {
    if (window.Toast) {
      Toast.error('拍照失敗，請重試');
    } else {
      alert('拍照失敗，請重試');
    }
  }

  /**
   * 攝像頭切換回調
   */
  handleCameraSwitch(data) {
    if (data.facingMode) {
      this.currentFacingMode = data.facingMode;
    }
  }

  /**
   * 顯示/隱藏加載指示器
   */
  showLoading(show) {
    this.loadingElement.style.display = show ? 'flex' : 'none';
  }

  /**
   * 更新攝像頭切換按鈕
   */
  updateSwitchButton() {
    this.switchButton.style.display = this.supportsCameraSwitch ? 'flex' : 'none';
  }

  /**
   * 銷毀組件
   */
  destroy() {
    this.stopCamera();
    
    if (this.focusTimeout) {
      clearTimeout(this.focusTimeout);
    }
    
    if (this.element && this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }
    
    // 恢復背景滾動
    document.body.style.overflow = '';
  }

  /**
   * 更新選項
   */
  updateOptions(newOptions) {
    this.options = { ...this.options, ...newOptions };
    
    if (newOptions.visible !== undefined) {
      if (newOptions.visible) {
        this.show();
      } else {
        this.hide();
      }
    }
  }
}

// 導出到全局
window.MobileCameraModal = MobileCameraModal;

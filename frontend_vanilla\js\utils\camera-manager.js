/**
 * 統一相機管理器 - 原生JavaScript版本
 * 根據環境自動選擇合適的拍照策略，提供統一的API接口
 */

/**
 * 相機管理器類
 */
class CameraManager {
  constructor() {
    this.strategy = null;
    this.mode = null;
    this.isInitialized = false;
    this.callbacks = {};
    this.capabilities = null;
  }

  /**
   * 初始化相機管理器
   */
  async initialize() {
    try {
      // 檢測相機功能
      this.capabilities = await getCameraCapabilities();
      
      if (!this.capabilities.hasCamera) {
        throw new Error('設備不支持相機功能');
      }

      // 獲取推薦的相機模式
      this.mode = await getRecommendedCameraMode();
      
      if (this.mode === 'none') {
        throw new Error('無可用的相機模式');
      }

      // 創建相應的策略
      this.strategy = createCameraStrategy(this.mode);
      
      // 設置策略回調
      this.strategy.setCallbacks({
        cameraStart: (data) => this.emit('cameraStart', data),
        cameraStop: () => this.emit('cameraStop'),
        cameraError: (error) => this.emit('cameraError', error),
        photoTaken: (data) => this.emit('photoTaken', data),
        photoError: (error) => this.emit('photoError', error),
        cameraSwitch: (data) => this.emit('cameraSwitch', data),
        cameraSwitchError: (error) => this.emit('cameraSwitchError', error)
      });

      this.isInitialized = true;
      console.log(`相機管理器初始化完成，模式: ${this.mode}`);
      
    } catch (error) {
      console.error('相機管理器初始化失敗:', error);
      throw error;
    }
  }

  /**
   * 設置事件回調
   */
  setCallbacks(callbacks) {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  /**
   * 觸發事件
   */
  emit(event, data) {
    if (this.callbacks[event]) {
      this.callbacks[event](data);
    }
  }

  /**
   * 啟動相機
   */
  async startCamera(target = 'back', options = {}) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (!this.strategy) {
      throw new Error('相機策略未初始化');
    }

    try {
      // 設置視頻和畫布元素（如果提供）
      if (options.videoElement && options.canvasElement) {
        this.strategy.setElements(options.videoElement, options.canvasElement);
      }

      // 構建相機約束
      const constraints = this.buildConstraints(target, options.constraints);
      
      console.log(`啟動相機 - 模式: ${this.mode}, 目標: ${target}`);
      
      const stream = await this.strategy.startCamera(constraints);
      
      return stream;
    } catch (error) {
      console.error('啟動相機失敗:', error);
      
      // 嘗試降級處理
      if (error.name === 'NotAllowedError') {
        const permissionError = new Error('相機權限被拒絕，請允許相機權限後重試');
        permissionError.name = 'PermissionError';
        throw permissionError;
      } else if (error.name === 'NotFoundError') {
        const deviceError = new Error('未找到相機設備');
        deviceError.name = 'DeviceError';
        throw deviceError;
      } else if (error.name === 'NotReadableError') {
        const occupiedError = new Error('相機被其他應用占用');
        occupiedError.name = 'OccupiedError';
        throw occupiedError;
      }
      
      throw error;
    }
  }

  /**
   * 構建相機約束
   */
  buildConstraints(target, customConstraints = {}) {
    const baseConstraints = {
      video: {
        facingMode: target === 'front' ? 'user' : 'environment',
        width: { ideal: 1920, max: 1920 },
        height: { ideal: 1080, max: 1080 }
      }
    };

    // 合併自定義約束
    if (customConstraints.video) {
      baseConstraints.video = {
        ...baseConstraints.video,
        ...customConstraints.video
      };
    }

    return baseConstraints;
  }

  /**
   * 停止相機
   */
  stopCamera() {
    if (this.strategy) {
      this.strategy.stopCamera();
    }
  }

  /**
   * 拍照
   */
  async takePhoto() {
    if (!this.strategy) {
      throw new Error('相機未啟動');
    }

    try {
      const photoData = await this.strategy.takePhoto();
      console.log('拍照成功:', {
        width: photoData.width,
        height: photoData.height,
        size: photoData.blob.size
      });
      
      return photoData;
    } catch (error) {
      console.error('拍照失敗:', error);
      throw error;
    }
  }

  /**
   * 切換攝像頭（僅移動端）
   */
  async switchCamera() {
    if (!this.strategy) {
      throw new Error('相機未啟動');
    }

    if (this.mode !== 'mobile') {
      throw new Error('只有移動端模式支持攝像頭切換');
    }

    try {
      const newFacingMode = await this.strategy.switchCamera();
      console.log('攝像頭切換成功:', newFacingMode);
      return newFacingMode;
    } catch (error) {
      console.error('攝像頭切換失敗:', error);
      throw error;
    }
  }

  /**
   * 獲取相機狀態
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      mode: this.mode,
      capabilities: this.capabilities,
      strategy: this.strategy ? this.strategy.getStatus() : null
    };
  }

  /**
   * 獲取當前模式
   */
  getMode() {
    return this.mode;
  }

  /**
   * 獲取相機功能
   */
  getCapabilities() {
    return this.capabilities;
  }

  /**
   * 檢查是否支持攝像頭切換
   */
  supportsCameraSwitch() {
    return this.mode === 'mobile' && 
           this.strategy && 
           typeof this.strategy.supportsCameraSwitch === 'function' && 
           this.strategy.supportsCameraSwitch();
  }

  /**
   * 獲取當前攝像頭模式（僅移動端）
   */
  getCurrentFacingMode() {
    if (this.mode === 'mobile' && this.strategy && typeof this.strategy.getCurrentFacingMode === 'function') {
      return this.strategy.getCurrentFacingMode();
    }
    return null;
  }

  /**
   * 設置視頻和畫布元素
   */
  setElements(videoElement, canvasElement) {
    if (this.strategy && typeof this.strategy.setElements === 'function') {
      this.strategy.setElements(videoElement, canvasElement);
    }
  }

  /**
   * 銷毀管理器
   */
  destroy() {
    this.stopCamera();
    this.strategy = null;
    this.callbacks = {};
    this.isInitialized = false;
  }
}

// 創建單例實例
let cameraManagerInstance = null;

/**
 * 獲取相機管理器實例
 */
function getCameraManager() {
  if (!cameraManagerInstance) {
    cameraManagerInstance = new CameraManager();
  }
  return cameraManagerInstance;
}

/**
 * 重置相機管理器（用於測試）
 */
function resetCameraManager() {
  if (cameraManagerInstance) {
    cameraManagerInstance.destroy();
  }
  cameraManagerInstance = null;
}

// 導出到全局
window.CameraManager = CameraManager;
window.getCameraManager = getCameraManager;
window.resetCameraManager = resetCameraManager;

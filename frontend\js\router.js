/**
 * 原生JavaScript路由系統 - 替代React Router
 */

class Router {
  constructor() {
    this.routes = new Map();
    this.currentRoute = '/';
    this.currentComponent = null;
    this.init();
  }

  init() {
    // 監聽瀏覽器前進後退
    window.addEventListener('popstate', (event) => {
      this.handleRouteChange(window.location.pathname);
    });

    // 攔截所有連結點擊
    document.addEventListener('click', (event) => {
      const link = event.target.closest('a[data-route]');
      if (link) {
        event.preventDefault();
        const path = link.getAttribute('data-route') || link.getAttribute('href');
        this.navigate(path);
      }
    });
  }

  /**
   * 啟動路由系統（在路由註冊完成後調用）
   */
  start() {
    console.log('🛣️ 啟動路由系統，當前路徑:', window.location.pathname);
    this.handleRouteChange(window.location.pathname);
  }

  /**
   * 註冊路由
   * @param {string} path - 路由路徑
   * @param {Function} component - 組件構造函數
   */
  register(path, component) {
    this.routes.set(path, component);
  }

  /**
   * 導航到指定路由
   * @param {string} path - 目標路徑
   * @param {boolean} replace - 是否替換當前歷史記錄
   */
  navigate(path, replace = false) {
    if (path === this.currentRoute) {
      return;
    }

    // 更新瀏覽器歷史記錄
    if (replace) {
      window.history.replaceState(null, '', path);
    } else {
      window.history.pushState(null, '', path);
    }

    this.handleRouteChange(path);
  }

  /**
   * 處理路由變化
   * @param {string} path - 新路徑
   */
  handleRouteChange(path) {
    console.log('🔄 路由變化:', path);
    console.log('📋 已註冊路由:', Array.from(this.routes.keys()));

    // 清理當前組件
    if (this.currentComponent && typeof this.currentComponent.unmount === 'function') {
      this.currentComponent.unmount();
    }

    // 查找匹配的路由
    const matchedRoute = this.findMatchingRoute(path);

    if (matchedRoute) {
      const { component: ComponentClass, params } = matchedRoute;
      console.log('✅ 找到匹配路由:', ComponentClass.name, params);

      try {
        // 創建新組件實例
        this.currentComponent = new ComponentClass({ params });
        this.currentRoute = path;

        // 渲染組件
        this.render();

        // 調用組件的掛載方法
        if (typeof this.currentComponent.mount === 'function') {
          this.currentComponent.mount();
        }

        console.log('✅ 路由渲染完成:', path);
      } catch (error) {
        console.error('❌ 路由渲染錯誤:', error);
        this.handleNotFound();
      }
    } else {
      console.log('❌ 未找到匹配路由:', path);
      this.handleNotFound();
    }
  }

  /**
   * 查找匹配的路由
   * @param {string} path - 路徑
   * @returns {Object|null} 匹配結果
   */
  findMatchingRoute(path) {
    // 精確匹配
    if (this.routes.has(path)) {
      return {
        component: this.routes.get(path),
        params: {}
      };
    }

    // 參數匹配 (例如 /cards/:id)
    for (const [routePath, component] of this.routes) {
      const params = this.matchRoute(routePath, path);
      if (params !== null) {
        return {
          component,
          params
        };
      }
    }

    return null;
  }

  /**
   * 匹配路由參數
   * @param {string} routePath - 路由模式
   * @param {string} actualPath - 實際路徑
   * @returns {Object|null} 參數對象
   */
  matchRoute(routePath, actualPath) {
    const routeParts = routePath.split('/');
    const actualParts = actualPath.split('/');

    if (routeParts.length !== actualParts.length) {
      return null;
    }

    const params = {};
    
    for (let i = 0; i < routeParts.length; i++) {
      const routePart = routeParts[i];
      const actualPart = actualParts[i];

      if (routePart.startsWith(':')) {
        // 參數部分
        const paramName = routePart.slice(1);
        params[paramName] = actualPart;
      } else if (routePart !== actualPart) {
        // 不匹配
        return null;
      }
    }

    return params;
  }

  /**
   * 渲染當前組件
   */
  render() {
    const appContainer = document.getElementById('app');
    if (!appContainer) {
      console.error('找不到應用容器 #app');
      return;
    }

    if (this.currentComponent && typeof this.currentComponent.render === 'function') {
      try {
        const html = this.currentComponent.render();
        appContainer.innerHTML = html;
        
        // 添加淡入動畫
        appContainer.classList.add('fade-in');
        setTimeout(() => {
          appContainer.classList.remove('fade-in');
        }, 300);
      } catch (error) {
        console.error('組件渲染錯誤:', error);
        this.handleNotFound();
      }
    }
  }

  /**
   * 處理404情況
   */
  handleNotFound() {
    console.log('❌ 404 - 頁面未找到:', window.location.pathname);

    const appContainer = document.getElementById('app');
    if (appContainer) {
      appContainer.innerHTML = `
        <div class="App" style="display: flex; align-items: center; justify-content: center; min-height: 100vh;">
          <div class="card" style="max-width: 400px; text-align: center; margin: 20px;">
            <div class="card-body">
              <h2 style="margin-bottom: 16px; color: var(--error-color);">
                <i class="fas fa-exclamation-triangle"></i> 頁面未找到
              </h2>
              <p style="margin-bottom: 8px; color: var(--text-color-secondary);">
                抱歉，您訪問的頁面不存在。
              </p>
              <p style="margin-bottom: 24px; color: var(--text-color-disabled); font-size: 14px;">
                路徑: ${window.location.pathname}
              </p>
              <button class="btn btn-primary btn-large" id="backToHome">
                <i class="fas fa-home"></i> 返回首頁
              </button>
            </div>
          </div>
        </div>
      `;

      // 綁定返回首頁按鈕事件
      const backButton = document.getElementById('backToHome');
      if (backButton) {
        backButton.addEventListener('click', () => {
          this.navigate('/');
        });
      }
    }
  }

  /**
   * 獲取當前路由
   * @returns {string} 當前路由路徑
   */
  getCurrentRoute() {
    return this.currentRoute;
  }

  /**
   * 獲取當前組件
   * @returns {Object} 當前組件實例
   */
  getCurrentComponent() {
    return this.currentComponent;
  }

  /**
   * 返回上一頁
   */
  goBack() {
    window.history.back();
  }

  /**
   * 前進到下一頁
   */
  goForward() {
    window.history.forward();
  }

  /**
   * 替換當前路由
   * @param {string} path - 新路徑
   */
  replace(path) {
    this.navigate(path, true);
  }
}

// 創建全局路由實例
const router = new Router();

// 導出到全局
window.router = router;

// 工具函數：創建路由連結
function createRouteLink(path, text, className = '') {
  return `<a href="${path}" data-route="${path}" class="${className}">${text}</a>`;
}

// 工具函數：獲取路由參數
function getRouteParams() {
  return router.getCurrentComponent()?.props?.params || {};
}

// 導出工具函數
window.createRouteLink = createRouteLink;
window.getRouteParams = getRouteParams;

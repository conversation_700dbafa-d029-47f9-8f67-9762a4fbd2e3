/**
 * 掃描上傳頁面 - 原生JavaScript版本
 */

class ScanUploadPage extends Component {
  constructor(props = {}) {
    super(props);
    this.state = {
      loading: false,
      frontImage: { file: null, preview: null, ocrText: '', parseStatus: null },
      backImage: { file: null, preview: null, ocrText: '', parseStatus: null },
      currentCaptureTarget: 'front',
      cameraModalVisible: false,
      parseLoading: false,
      cardData: {
        name: '',
        company_name: '',
        position: '',
        mobile_phone: '',
        office_phone: '',
        email: '',
        line_id: '',
        notes: '',
        company_address_1: '',
        company_address_2: ''
      }
    };
    
    this.cameraManager = null;
    this.mobileCameraModal = null;
    this.isMobileCameraMode = false;
  }

  render() {
    return `
      <div class="App">
        <!-- 導航欄 -->
        <div class="navbar">
          <button class="navbar-back" onclick="this.goBack()">
            <i class="fas fa-arrow-left"></i>
          </button>
          <div class="navbar-title">掃描 / 上傳名片</div>
          <div class="navbar-right"></div>
        </div>

        <div class="content">
          <!-- 圖片拍攝/上傳區域 -->
          <div class="card mb-md">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-camera"></i> 名片圖片
              </h3>
            </div>
            <div class="card-body">
              <!-- 正面/背面切換 -->
              <div class="space mb-md">
                <button 
                  class="btn ${this.state.currentCaptureTarget === 'front' ? 'btn-primary' : 'btn-default'}" 
                  onclick="this.switchCaptureTarget('front')"
                >
                  正面
                </button>
                <button 
                  class="btn ${this.state.currentCaptureTarget === 'back' ? 'btn-primary' : 'btn-default'}" 
                  onclick="this.switchCaptureTarget('back')"
                >
                  背面
                </button>
              </div>

              <!-- 當前圖片顯示 -->
              <div class="camera-container">
                ${this.renderCurrentImage()}
              </div>

              <!-- 操作按鈕 -->
              <div class="space mt-md">
                <button class="btn btn-primary" onclick="this.startCamera()">
                  <i class="fas fa-camera"></i> 拍照
                </button>
                <button class="btn btn-default" onclick="this.selectFile()">
                  <i class="fas fa-upload"></i> 選擇文件
                </button>
                ${this.renderOCRButton()}
              </div>
              
              <!-- 隱藏的文件輸入 -->
              <input type="file" id="fileInput" accept="image/*" style="display: none;" onchange="this.handleFileSelect(event)">
            </div>
          </div>

          <!-- OCR結果和名片資料表單 -->
          ${this.renderCardDataForm()}

          <!-- 保存按鈕 -->
          <div class="space space-vertical">
            <button 
              class="btn btn-success btn-large btn-block" 
              onclick="this.handleSave()"
              ${this.state.loading ? 'disabled' : ''}
            >
              <i class="fas fa-check"></i> 保存名片
            </button>
          </div>
        </div>

        <!-- 相機模態框容器 -->
        <div id="camera-modal-placeholder"></div>
      </div>
    `;
  }

  renderCurrentImage() {
    const currentImage = this.state.currentCaptureTarget === 'front' ? this.state.frontImage : this.state.backImage;
    
    if (currentImage.preview) {
      return `
        <img 
          src="${currentImage.preview}" 
          alt="${this.state.currentCaptureTarget === 'front' ? '正面' : '背面'}名片" 
          class="preview-image"
        >
        <button class="btn btn-danger" onclick="this.clearCurrentImage()" style="position: absolute; top: 8px; right: 8px;">
          <i class="fas fa-times"></i>
        </button>
      `;
    } else {
      return `
        <div class="upload-area" onclick="this.selectFile()">
          <i class="fas fa-image" style="font-size: 48px; color: var(--text-color-secondary); margin-bottom: 16px;"></i>
          <div class="upload-text">點擊選擇${this.state.currentCaptureTarget === 'front' ? '正面' : '背面'}名片圖片</div>
        </div>
      `;
    }
  }

  renderOCRButton() {
    const currentImage = this.state.currentCaptureTarget === 'front' ? this.state.frontImage : this.state.backImage;
    
    if (currentImage.file) {
      return `
        <button 
          class="btn btn-warning" 
          onclick="this.performOCR()"
          ${this.state.parseLoading ? 'disabled' : ''}
        >
          <i class="fas fa-eye"></i> 
          ${this.state.parseLoading ? '識別中...' : '識別文字'}
        </button>
      `;
    }
    return '';
  }

  renderCardDataForm() {
    return `
      <div class="card mb-md">
        <div class="card-header">
          <h3 class="card-title">
            <i class="fas fa-user-edit"></i> 名片資料
          </h3>
        </div>
        <div class="card-body">
          <div class="form">
            <!-- 基本資訊 -->
            <div class="divider-text">基本資訊</div>
            
            <div class="form-item">
              <label class="form-label required">姓名</label>
              <input 
                type="text" 
                class="form-input" 
                placeholder="請輸入姓名" 
                value="${this.state.cardData.name}"
                onchange="this.updateCardData('name', this.value)"
              >
            </div>

            <div class="form-item">
              <label class="form-label">公司名稱</label>
              <input 
                type="text" 
                class="form-input" 
                placeholder="請輸入公司名稱" 
                value="${this.state.cardData.company_name}"
                onchange="this.updateCardData('company_name', this.value)"
              >
            </div>

            <div class="form-item">
              <label class="form-label">職位</label>
              <input 
                type="text" 
                class="form-input" 
                placeholder="請輸入職位" 
                value="${this.state.cardData.position}"
                onchange="this.updateCardData('position', this.value)"
              >
            </div>

            <!-- 聯絡資訊 -->
            <div class="divider-text">聯絡資訊</div>

            <div class="form-item">
              <label class="form-label">手機</label>
              <input 
                type="tel" 
                class="form-input" 
                placeholder="請輸入手機號碼" 
                value="${this.state.cardData.mobile_phone}"
                onchange="this.updateCardData('mobile_phone', this.value)"
              >
            </div>

            <div class="form-item">
              <label class="form-label">公司電話</label>
              <input 
                type="tel" 
                class="form-input" 
                placeholder="請輸入公司電話" 
                value="${this.state.cardData.office_phone}"
                onchange="this.updateCardData('office_phone', this.value)"
              >
            </div>

            <div class="form-item">
              <label class="form-label">Email</label>
              <input 
                type="email" 
                class="form-input" 
                placeholder="請輸入Email地址" 
                value="${this.state.cardData.email}"
                onchange="this.updateCardData('email', this.value)"
              >
            </div>

            <div class="form-item">
              <label class="form-label">Line ID</label>
              <input 
                type="text" 
                class="form-input" 
                placeholder="請輸入Line ID" 
                value="${this.state.cardData.line_id}"
                onchange="this.updateCardData('line_id', this.value)"
              >
            </div>

            <!-- 地址資訊 -->
            <div class="divider-text">地址資訊</div>

            <div class="form-item">
              <label class="form-label">公司地址一</label>
              <input 
                type="text" 
                class="form-input" 
                placeholder="請輸入公司地址" 
                value="${this.state.cardData.company_address_1}"
                onchange="this.updateCardData('company_address_1', this.value)"
              >
            </div>

            <div class="form-item">
              <label class="form-label">公司地址二</label>
              <input 
                type="text" 
                class="form-input" 
                placeholder="請輸入公司地址（續）" 
                value="${this.state.cardData.company_address_2}"
                onchange="this.updateCardData('company_address_2', this.value)"
              >
            </div>

            <!-- 備註 -->
            <div class="divider-text">其他資訊</div>

            <div class="form-item">
              <label class="form-label">備註</label>
              <textarea 
                class="form-textarea" 
                placeholder="請輸入備註信息" 
                rows="3"
                onchange="this.updateCardData('notes', this.value)"
              >${this.state.cardData.notes}</textarea>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  async mount() {
    // 初始化相機管理器
    await this.initCameraManager();
    
    // 綁定事件
    this.bindEvents();
    
    // 檢測移動端相機模式
    this.detectCameraMode();
  }

  async initCameraManager() {
    try {
      this.cameraManager = getCameraManager();
      if (!this.cameraManager.isInitialized) {
        await this.cameraManager.initialize();
      }
    } catch (error) {
      console.error('相機管理器初始化失敗:', error);
    }
  }

  bindEvents() {
    // 綁定所有onclick事件
    const buttons = document.querySelectorAll('button[onclick], div[onclick]');
    buttons.forEach(element => {
      const onclickStr = element.getAttribute('onclick');
      if (onclickStr) {
        element.onclick = (event) => {
          event.preventDefault();
          this.executeOnclickFunction(onclickStr, event);
        };
      }
    });

    // 綁定輸入事件
    const inputs = document.querySelectorAll('input[onchange], textarea[onchange]');
    inputs.forEach(element => {
      const onchangeStr = element.getAttribute('onchange');
      if (onchangeStr) {
        element.onchange = (event) => {
          this.executeOnchangeFunction(onchangeStr, event);
        };
      }
    });
  }

  executeOnclickFunction(funcStr, event) {
    // 解析並執行onclick函數
    try {
      const func = new Function('event', `return (${funcStr}).call(this, event)`);
      func.call(this, event);
    } catch (error) {
      console.error('執行onclick函數失敗:', error);
    }
  }

  executeOnchangeFunction(funcStr, event) {
    // 解析並執行onchange函數
    try {
      const func = new Function('event', `
        const target = event.target;
        const value = target.value;
        return (${funcStr}).call(target, event);
      `);
      func.call(this, event);
    } catch (error) {
      console.error('執行onchange函數失敗:', error);
    }
  }

  detectCameraMode() {
    this.isMobileCameraMode = isMobileDevice();
  }

  goBack() {
    router.goBack();
  }

  switchCaptureTarget(target) {
    this.setState({ currentCaptureTarget: target });
  }

  selectFile() {
    const fileInput = document.getElementById('fileInput');
    if (fileInput) {
      fileInput.click();
    }
  }

  handleFileSelect(event) {
    const file = event.target.files[0];
    if (file) {
      this.handleImageUpload(file);
    }
  }

  async handleImageUpload(file) {
    const reader = new FileReader();
    reader.onload = (e) => {
      const target = this.state.currentCaptureTarget;
      const newState = { ...this.state };
      
      if (target === 'front') {
        newState.frontImage = {
          file,
          preview: e.target.result,
          ocrText: '',
          parseStatus: null
        };
      } else {
        newState.backImage = {
          file,
          preview: e.target.result,
          ocrText: '',
          parseStatus: null
        };
      }
      
      this.setState(newState);
    };
    reader.readAsDataURL(file);

    // 自動執行OCR
    await this.performOCR(file);
  }

  clearCurrentImage() {
    const target = this.state.currentCaptureTarget;
    const newState = { ...this.state };
    
    if (target === 'front') {
      newState.frontImage = { file: null, preview: null, ocrText: '', parseStatus: null };
    } else {
      newState.backImage = { file: null, preview: null, ocrText: '', parseStatus: null };
    }
    
    this.setState(newState);
  }

  async startCamera() {
    if (!this.cameraManager) {
      Toast.error('相機管理器未初始化');
      return;
    }

    try {
      if (this.isMobileCameraMode) {
        // 移動端：使用全屏相機
        this.showMobileCamera();
      } else {
        // Web端：使用Modal相機
        this.showWebCamera();
      }
    } catch (error) {
      console.error('啟動相機失敗:', error);
      Toast.error('相機啟動失敗，請檢查權限');
    }
  }

  showMobileCamera() {
    if (!this.mobileCameraModal) {
      this.mobileCameraModal = new MobileCameraModal({
        visible: false,
        onClose: () => this.hideMobileCamera(),
        onPhotoTaken: (photoData) => this.handleMobilePhotoTaken(photoData),
        cameraManager: this.cameraManager,
        target: this.state.currentCaptureTarget === 'front' ? 'front' : 'back'
      });
    }
    
    this.mobileCameraModal.updateOptions({
      visible: true,
      target: this.state.currentCaptureTarget === 'front' ? 'front' : 'back'
    });
  }

  hideMobileCamera() {
    if (this.mobileCameraModal) {
      this.mobileCameraModal.updateOptions({ visible: false });
    }
  }

  async handleMobilePhotoTaken(photoData) {
    const file = uploadUtils.blobToFile(photoData.blob, `${this.state.currentCaptureTarget}_card.jpg`);
    await this.handleImageUpload(file);
  }

  showWebCamera() {
    // Web端相機實現
    const modal = Modal.show({
      title: '拍攝名片',
      content: `
        <div class="camera-modal">
          <video id="cameraVideo" autoplay playsinline style="width: 100%; height: 350px; object-fit: cover; border-radius: 8px; background: #000;"></video>
          <canvas id="cameraCanvas" style="display: none;"></canvas>
          <div style="margin-top: 16px; text-align: center;">
            <button class="btn btn-primary btn-large" onclick="this.capturePhoto()">
              <i class="fas fa-camera"></i> 拍照
            </button>
          </div>
        </div>
      `
    });

    // 啟動Web相機
    setTimeout(async () => {
      const video = document.getElementById('cameraVideo');
      const canvas = document.getElementById('cameraCanvas');
      
      if (video && canvas) {
        try {
          this.cameraManager.setElements(video, canvas);
          await this.cameraManager.startCamera(this.state.currentCaptureTarget === 'front' ? 'front' : 'back');
          
          // 綁定拍照按鈕
          window.capturePhoto = async () => {
            try {
              const photoData = await this.cameraManager.takePhoto();
              const file = uploadUtils.blobToFile(photoData.blob, `${this.state.currentCaptureTarget}_card.jpg`);
              await this.handleImageUpload(file);
              modal.hide();
            } catch (error) {
              console.error('拍照失敗:', error);
              Toast.error('拍照失敗，請重試');
            }
          };
        } catch (error) {
          console.error('Web相機啟動失敗:', error);
          Toast.error('相機啟動失敗');
          modal.hide();
        }
      }
    }, 100);
  }

  async performOCR(file) {
    const currentImage = this.state.currentCaptureTarget === 'front' ? this.state.frontImage : this.state.backImage;
    const targetFile = file || currentImage.file;
    
    if (!targetFile) {
      Toast.error('請先選擇或拍攝圖片');
      return;
    }

    this.setState({ parseLoading: true });

    try {
      // 執行OCR識別
      const ocrResult = await ocrApi.recognize(targetFile);
      
      if (ocrResult.text) {
        // 更新OCR文本
        const newState = { ...this.state };
        if (this.state.currentCaptureTarget === 'front') {
          newState.frontImage.ocrText = ocrResult.text;
        } else {
          newState.backImage.ocrText = ocrResult.text;
        }
        this.setState(newState);

        // 解析OCR文本為結構化數據
        await this.parseOCRData(ocrResult.text);
      } else {
        Toast.warning('未識別到文字內容');
      }
    } catch (error) {
      console.error('OCR識別失敗:', error);
      Toast.error('文字識別失敗，請重試');
    } finally {
      this.setState({ parseLoading: false });
    }
  }

  async parseOCRData(ocrText) {
    try {
      const parseResult = await ocrApi.parse(ocrText);
      
      if (parseResult.data) {
        // 合併解析結果到名片數據
        const newCardData = { ...this.state.cardData };
        Object.keys(parseResult.data).forEach(key => {
          if (parseResult.data[key] && !newCardData[key]) {
            newCardData[key] = parseResult.data[key];
          }
        });
        
        this.setState({ cardData: newCardData });
        Toast.success('文字識別完成');
      }
    } catch (error) {
      console.error('OCR解析失敗:', error);
      Toast.warning('文字解析失敗，請手動填寫');
    }
  }

  updateCardData(field, value) {
    const newCardData = { ...this.state.cardData };
    newCardData[field] = value;
    this.setState({ cardData: newCardData });
  }

  async handleSave() {
    // 驗證必填欄位
    if (!this.state.cardData.name.trim()) {
      Toast.error('請輸入姓名');
      return;
    }

    this.setState({ loading: true });

    try {
      const saveData = { ...this.state.cardData };
      
      // 添加圖片文件
      if (this.state.frontImage.file) {
        saveData.front_image = this.state.frontImage.file;
      }
      if (this.state.backImage.file) {
        saveData.back_image = this.state.backImage.file;
      }

      const response = await cardsApi.create(saveData);

      if (response) {
        Toast.success('名片保存成功！');
        router.navigate('/cards');
      }
    } catch (error) {
      console.error('保存失敗:', error);
      errorHandler.showError(error);
    } finally {
      this.setState({ loading: false });
    }
  }

  unmount() {
    // 停止相機
    if (this.cameraManager) {
      this.cameraManager.stopCamera();
    }
    
    // 銷毀移動端相機模態框
    if (this.mobileCameraModal) {
      this.mobileCameraModal.destroy();
      this.mobileCameraModal = null;
    }
  }
}

// 導出到全局
window.ScanUploadPage = ScanUploadPage;

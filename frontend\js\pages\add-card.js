/**
 * 手動新增名片頁面 - 原生JavaScript版本
 */

class AddCardPage extends Component {
  constructor(props = {}) {
    super(props);
    this.state = {
      loading: false,
      cardData: {
        name: '',
        company_name: '',
        position: '',
        mobile_phone: '',
        office_phone: '',
        email: '',
        line_id: '',
        notes: '',
        company_address_1: '',
        company_address_2: ''
      }
    };
  }

  render() {
    return `
      <div class="App">
        <!-- 導航欄 -->
        <div class="navbar">
          <button class="navbar-back" onclick="this.goBack()">
            <i class="fas fa-arrow-left"></i>
          </button>
          <div class="navbar-title">手動新增名片</div>
          <div class="navbar-right"></div>
        </div>

        <div class="content">
          <!-- 名片資料表單 -->
          <div class="card mb-md">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-user-plus"></i> 名片資料
              </h3>
            </div>
            <div class="card-body">
              <div class="form">
                <!-- 基本資訊 -->
                <div class="divider-text">基本資訊</div>
                
                <div class="form-item">
                  <label class="form-label required">姓名</label>
                  <input 
                    type="text" 
                    class="form-input" 
                    placeholder="請輸入姓名" 
                    value="${this.state.cardData.name}"
                    onchange="this.updateCardData('name', this.value)"
                  >
                </div>

                <div class="form-item">
                  <label class="form-label">公司名稱</label>
                  <input 
                    type="text" 
                    class="form-input" 
                    placeholder="請輸入公司名稱" 
                    value="${this.state.cardData.company_name}"
                    onchange="this.updateCardData('company_name', this.value)"
                  >
                </div>

                <div class="form-item">
                  <label class="form-label">職位</label>
                  <input 
                    type="text" 
                    class="form-input" 
                    placeholder="請輸入職位" 
                    value="${this.state.cardData.position}"
                    onchange="this.updateCardData('position', this.value)"
                  >
                </div>

                <!-- 聯絡資訊 -->
                <div class="divider-text">聯絡資訊</div>

                <div class="form-item">
                  <label class="form-label">手機</label>
                  <input 
                    type="tel" 
                    class="form-input" 
                    placeholder="請輸入手機號碼" 
                    value="${this.state.cardData.mobile_phone}"
                    onchange="this.updateCardData('mobile_phone', this.value)"
                  >
                </div>

                <div class="form-item">
                  <label class="form-label">公司電話</label>
                  <input 
                    type="tel" 
                    class="form-input" 
                    placeholder="請輸入公司電話" 
                    value="${this.state.cardData.office_phone}"
                    onchange="this.updateCardData('office_phone', this.value)"
                  >
                </div>

                <div class="form-item">
                  <label class="form-label">Email</label>
                  <input 
                    type="email" 
                    class="form-input" 
                    placeholder="請輸入Email地址" 
                    value="${this.state.cardData.email}"
                    onchange="this.updateCardData('email', this.value)"
                  >
                </div>

                <div class="form-item">
                  <label class="form-label">Line ID</label>
                  <input 
                    type="text" 
                    class="form-input" 
                    placeholder="請輸入Line ID" 
                    value="${this.state.cardData.line_id}"
                    onchange="this.updateCardData('line_id', this.value)"
                  >
                </div>

                <!-- 地址資訊 -->
                <div class="divider-text">地址資訊</div>

                <div class="form-item">
                  <label class="form-label">公司地址一</label>
                  <input 
                    type="text" 
                    class="form-input" 
                    placeholder="請輸入公司地址" 
                    value="${this.state.cardData.company_address_1}"
                    onchange="this.updateCardData('company_address_1', this.value)"
                  >
                </div>

                <div class="form-item">
                  <label class="form-label">公司地址二</label>
                  <input 
                    type="text" 
                    class="form-input" 
                    placeholder="請輸入公司地址（續）" 
                    value="${this.state.cardData.company_address_2}"
                    onchange="this.updateCardData('company_address_2', this.value)"
                  >
                </div>

                <!-- 備註 -->
                <div class="divider-text">其他資訊</div>

                <div class="form-item">
                  <label class="form-label">備註</label>
                  <textarea 
                    class="form-textarea" 
                    placeholder="請輸入備註信息" 
                    rows="4"
                    onchange="this.updateCardData('notes', this.value)"
                  >${this.state.cardData.notes}</textarea>
                </div>
              </div>
            </div>
          </div>

          <!-- 操作按鈕 -->
          <div class="space space-vertical">
            <button 
              class="btn btn-success btn-large btn-block" 
              onclick="this.handleSave()"
              ${this.state.loading ? 'disabled' : ''}
            >
              <i class="fas fa-check icon"></i>
              ${this.state.loading ? '保存中...' : '保存名片'}
            </button>
            
            <button 
              class="btn btn-default btn-large btn-block" 
              onclick="this.handleReset()"
              ${this.state.loading ? 'disabled' : ''}
            >
              <i class="fas fa-undo icon"></i>
              重置表單
            </button>
          </div>

          <!-- 提示信息 -->
          <div class="card mt-md">
            <div class="card-body">
              <div style="display: flex; align-items: center; color: var(--text-color-secondary); font-size: 14px;">
                <i class="fas fa-info-circle" style="margin-right: 8px; color: var(--primary-color);"></i>
                <span>標有 * 的欄位為必填項目</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  mount() {
    this.bindEvents();
    
    // 自動聚焦到姓名輸入框
    setTimeout(() => {
      const nameInput = document.querySelector('input[placeholder="請輸入姓名"]');
      if (nameInput) {
        nameInput.focus();
      }
    }, 100);
  }

  bindEvents() {
    // 綁定所有onclick事件
    const buttons = document.querySelectorAll('button[onclick]');
    buttons.forEach(element => {
      const onclickStr = element.getAttribute('onclick');
      if (onclickStr) {
        element.onclick = (event) => {
          event.preventDefault();
          this.executeOnclickFunction(onclickStr, event);
        };
      }
    });

    // 綁定輸入事件
    const inputs = document.querySelectorAll('input[onchange], textarea[onchange]');
    inputs.forEach(element => {
      const onchangeStr = element.getAttribute('onchange');
      if (onchangeStr) {
        element.onchange = (event) => {
          this.executeOnchangeFunction(onchangeStr, event);
        };
      }
    });

    // 綁定表單驗證
    this.bindFormValidation();
  }

  bindFormValidation() {
    // 實時驗證必填欄位
    const nameInput = document.querySelector('input[placeholder="請輸入姓名"]');
    if (nameInput) {
      nameInput.addEventListener('input', (event) => {
        this.validateField('name', event.target.value);
      });
    }

    // 驗證Email格式
    const emailInput = document.querySelector('input[type="email"]');
    if (emailInput) {
      emailInput.addEventListener('blur', (event) => {
        this.validateEmail(event.target.value);
      });
    }

    // 驗證電話號碼格式
    const phoneInputs = document.querySelectorAll('input[type="tel"]');
    phoneInputs.forEach(input => {
      input.addEventListener('blur', (event) => {
        this.validatePhone(event.target.value);
      });
    });
  }

  executeOnclickFunction(funcStr, event) {
    try {
      const func = new Function('event', `return (${funcStr}).call(this, event)`);
      func.call(this, event);
    } catch (error) {
      console.error('執行onclick函數失敗:', error);
    }
  }

  executeOnchangeFunction(funcStr, event) {
    try {
      const func = new Function('event', `
        const target = event.target;
        const value = target.value;
        return (${funcStr}).call(target, event);
      `);
      func.call(this, event);
    } catch (error) {
      console.error('執行onchange函數失敗:', error);
    }
  }

  updateCardData(field, value) {
    const newCardData = { ...this.state.cardData };
    newCardData[field] = value;
    this.setState({ cardData: newCardData });
    
    // 實時驗證
    this.validateField(field, value);
  }

  validateField(field, value) {
    const input = document.querySelector(`input[onchange*="${field}"], textarea[onchange*="${field}"]`);
    if (!input) return;

    // 移除之前的錯誤樣式
    input.style.borderColor = '';
    
    // 驗證必填欄位
    if (field === 'name' && !value.trim()) {
      input.style.borderColor = 'var(--error-color)';
      return false;
    }

    return true;
  }

  validateEmail(email) {
    if (!email) return true;
    
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const input = document.querySelector('input[type="email"]');
    
    if (!emailRegex.test(email)) {
      input.style.borderColor = 'var(--error-color)';
      Toast.warning('請輸入有效的Email地址');
      return false;
    } else {
      input.style.borderColor = '';
      return true;
    }
  }

  validatePhone(phone) {
    if (!phone) return true;
    
    // 簡單的電話號碼驗證（允許數字、空格、連字符、括號）
    const phoneRegex = /^[\d\s\-\(\)\+]+$/;
    const input = event.target;
    
    if (!phoneRegex.test(phone)) {
      input.style.borderColor = 'var(--error-color)';
      Toast.warning('請輸入有效的電話號碼');
      return false;
    } else {
      input.style.borderColor = '';
      return true;
    }
  }

  goBack() {
    // 檢查是否有未保存的更改
    const hasChanges = Object.values(this.state.cardData).some(value => value.trim() !== '');
    
    if (hasChanges) {
      Dialog.confirm({
        title: '確認離開',
        content: '您有未保存的更改，確定要離開嗎？',
        onConfirm: () => router.goBack(),
        onCancel: () => {}
      });
    } else {
      router.goBack();
    }
  }

  async handleSave() {
    // 驗證必填欄位
    if (!this.state.cardData.name.trim()) {
      Toast.error('請輸入姓名');
      const nameInput = document.querySelector('input[placeholder="請輸入姓名"]');
      if (nameInput) {
        nameInput.focus();
        nameInput.style.borderColor = 'var(--error-color)';
      }
      return;
    }

    // 驗證Email格式
    if (this.state.cardData.email && !this.validateEmail(this.state.cardData.email)) {
      return;
    }

    this.setState({ loading: true });

    try {
      const response = await cardsApi.create(this.state.cardData);

      if (response) {
        Toast.success('名片新增成功！');
        router.navigate('/cards');
      }
    } catch (error) {
      console.error('保存失敗:', error);
      errorHandler.showError(error);
    } finally {
      this.setState({ loading: false });
    }
  }

  handleReset() {
    Dialog.confirm({
      title: '確認重置',
      content: '確定要清空所有已填寫的內容嗎？',
      onConfirm: () => {
        this.setState({
          cardData: {
            name: '',
            company_name: '',
            position: '',
            mobile_phone: '',
            office_phone: '',
            email: '',
            line_id: '',
            notes: '',
            company_address_1: '',
            company_address_2: ''
          }
        });
        
        // 清除所有輸入框的錯誤樣式
        const inputs = document.querySelectorAll('.form-input, .form-textarea');
        inputs.forEach(input => {
          input.style.borderColor = '';
        });
        
        Toast.success('表單已重置');
      }
    });
  }

  unmount() {
    // 清理事件監聽器
  }
}

// 導出到全局
window.AddCardPage = AddCardPage;

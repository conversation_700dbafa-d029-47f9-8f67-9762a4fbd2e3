/**
 * 相機策略實現 - 原生JavaScript版本
 * 提供不同環境下的相機拍照策略
 */

/**
 * 基礎相機策略類
 */
class BaseCameraStrategy {
  constructor() {
    this.stream = null;
    this.isActive = false;
    this.callbacks = {};
  }

  /**
   * 設置回調函數
   */
  setCallbacks(callbacks) {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  /**
   * 觸發事件
   */
  emit(event, data) {
    if (this.callbacks[event]) {
      this.callbacks[event](data);
    }
  }

  /**
   * 啟動相機 - 子類實現
   */
  async startCamera(constraints = {}) {
    throw new Error('startCamera method must be implemented by subclass');
  }

  /**
   * 停止相機
   */
  stopCamera() {
    if (this.stream) {
      this.stream.getTracks().forEach(track => track.stop());
      this.stream = null;
    }
    this.isActive = false;
    this.emit('cameraStop');
  }

  /**
   * 拍照 - 子類實現
   */
  async takePhoto() {
    throw new Error('takePhoto method must be implemented by subclass');
  }

  /**
   * 切換攝像頭
   */
  async switchCamera() {
    // 默認實現，子類可以覆蓋
    console.warn('Camera switching not implemented for this strategy');
  }

  /**
   * 獲取相機狀態
   */
  getStatus() {
    return {
      isActive: this.isActive,
      hasStream: !!this.stream
    };
  }
}

/**
 * Web端相機策略 - 使用Modal模式
 */
class WebCameraStrategy extends BaseCameraStrategy {
  constructor() {
    super();
    this.videoElement = null;
    this.canvasElement = null;
  }

  /**
   * 設置視頻和畫布元素
   */
  setElements(videoElement, canvasElement) {
    this.videoElement = videoElement;
    this.canvasElement = canvasElement;
  }

  /**
   * 啟動相機
   */
  async startCamera(constraints = {}) {
    try {
      if (!this.videoElement) {
        throw new Error('Video element not set');
      }

      const defaultConstraints = {
        video: {
          width: { ideal: 1920, max: 1920 },
          height: { ideal: 1080, max: 1080 },
          facingMode: 'environment'
        }
      };

      const finalConstraints = {
        video: {
          ...defaultConstraints.video,
          ...constraints.video
        }
      };

      this.stream = await navigator.mediaDevices.getUserMedia(finalConstraints);
      this.videoElement.srcObject = this.stream;
      this.isActive = true;

      this.emit('cameraStart', {
        facingMode: finalConstraints.video.facingMode
      });

      return this.stream;
    } catch (error) {
      console.error('Web相機啟動失敗:', error);
      this.emit('cameraError', error);
      throw error;
    }
  }

  /**
   * 拍照
   */
  async takePhoto() {
    try {
      if (!this.videoElement || !this.canvasElement || !this.stream) {
        throw new Error('Camera not ready for photo capture');
      }

      const video = this.videoElement;
      const canvas = this.canvasElement;
      const context = canvas.getContext('2d');

      // 設置畫布尺寸
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      // 繪製視頻幀到畫布
      context.drawImage(video, 0, 0, canvas.width, canvas.height);

      // 轉換為Blob
      return new Promise((resolve, reject) => {
        canvas.toBlob((blob) => {
          if (blob) {
            const photoData = {
              blob,
              dataUrl: canvas.toDataURL('image/jpeg', 0.9),
              width: canvas.width,
              height: canvas.height
            };
            
            this.emit('photoTaken', photoData);
            resolve(photoData);
          } else {
            const error = new Error('Failed to capture photo');
            this.emit('photoError', error);
            reject(error);
          }
        }, 'image/jpeg', 0.9);
      });
    } catch (error) {
      console.error('Web拍照失敗:', error);
      this.emit('photoError', error);
      throw error;
    }
  }
}

/**
 * 移動端相機策略 - 使用全屏模式
 */
class MobileCameraStrategy extends BaseCameraStrategy {
  constructor() {
    super();
    this.videoElement = null;
    this.canvasElement = null;
    this.currentFacingMode = 'environment'; // 默認後置攝像頭
    this.supportsFacingMode = true;
    this.availableCameras = [];
  }

  /**
   * 設置視頻和畫布元素
   */
  setElements(videoElement, canvasElement) {
    this.videoElement = videoElement;
    this.canvasElement = canvasElement;
  }

  /**
   * 獲取最佳相機約束
   */
  getBestCameraConstraints() {
    const baseConstraints = {
      facingMode: this.currentFacingMode,
      width: { ideal: 1920 },
      height: { ideal: 1080 }
    };

    return {
      highResConstraints: {
        video: {
          ...baseConstraints,
          width: { ideal: 3840, max: 3840 },
          height: { ideal: 2160, max: 2160 }
        }
      },
      standardConstraints: {
        video: {
          ...baseConstraints,
          width: { ideal: 1920, max: 1920 },
          height: { ideal: 1080, max: 1080 }
        }
      },
      fallbackConstraints: {
        video: {
          ...baseConstraints,
          width: { ideal: 1280, max: 1280 },
          height: { ideal: 720, max: 720 }
        }
      }
    };
  }

  /**
   * 嘗試啟動相機
   */
  async attemptCameraStart(constraints) {
    this.stream = await navigator.mediaDevices.getUserMedia(constraints);
    
    if (this.videoElement) {
      this.videoElement.srcObject = this.stream;
    }
    
    this.isActive = true;
    
    this.emit('cameraStart', {
      facingMode: this.currentFacingMode,
      constraints: constraints.video
    });
    
    return this.stream;
  }

  /**
   * 啟動相機
   */
  async startCamera(constraints = {}) {
    try {
      const { highResConstraints, standardConstraints, fallbackConstraints } = this.getBestCameraConstraints();

      // 如果用戶提供了自定義約束，使用用戶約束
      if (constraints.video) {
        const finalConstraints = {
          video: {
            ...standardConstraints.video,
            ...constraints.video
          }
        };
        return await this.attemptCameraStart(finalConstraints);
      }

      // 嘗試不同解析度級別
      const constraintLevels = [
        { name: '4K/高解析度', constraints: highResConstraints },
        { name: '2K/標準解析度', constraints: standardConstraints },
        { name: '1080p/備用解析度', constraints: fallbackConstraints }
      ];

      for (const level of constraintLevels) {
        try {
          console.log(`移動端嘗試${level.name}相機啟動...`);
          return await this.attemptCameraStart(level.constraints);
        } catch (error) {
          console.warn(`${level.name}啟動失敗，嘗試下一級別:`, error.message);
          continue;
        }
      }

      // 如果所有預設都失敗，嘗試基本約束
      throw new Error('所有解析度級別都無法啟動相機');

    } catch (error) {
      console.error('移動端相機啟動失敗:', error);

      // 如果指定的攝像頭模式失敗，嘗試基本模式
      if (error.name === 'OverconstrainedError' && this.currentFacingMode !== 'user') {
        try {
          this.currentFacingMode = 'user';
          return await this.startCamera({ video: { facingMode: 'user' } });
        } catch (fallbackError) {
          this.supportsFacingMode = false;
          return await this.startCamera({ video: true });
        }
      }

      this.emit('cameraError', error);
      throw error;
    }
  }

  /**
   * 拍照
   */
  async takePhoto() {
    try {
      if (!this.videoElement || !this.canvasElement || !this.stream) {
        throw new Error('Camera not ready for photo capture');
      }

      const video = this.videoElement;
      const canvas = this.canvasElement;
      const context = canvas.getContext('2d');

      // 設置畫布尺寸
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      // 繪製視頻幀到畫布
      context.drawImage(video, 0, 0, canvas.width, canvas.height);

      // 轉換為Blob
      return new Promise((resolve, reject) => {
        canvas.toBlob((blob) => {
          if (blob) {
            const photoData = {
              blob,
              dataUrl: canvas.toDataURL('image/jpeg', 0.9),
              width: canvas.width,
              height: canvas.height,
              facingMode: this.currentFacingMode
            };
            
            this.emit('photoTaken', photoData);
            resolve(photoData);
          } else {
            const error = new Error('Failed to capture photo');
            this.emit('photoError', error);
            reject(error);
          }
        }, 'image/jpeg', 0.9);
      });
    } catch (error) {
      console.error('移動端拍照失敗:', error);
      this.emit('photoError', error);
      throw error;
    }
  }

  /**
   * 切換攝像頭
   */
  async switchCamera() {
    if (!this.supportsFacingMode) {
      throw new Error('設備不支持攝像頭切換');
    }

    try {
      // 停止當前流
      this.stopCamera();

      // 切換攝像頭模式
      this.currentFacingMode = this.currentFacingMode === 'environment' ? 'user' : 'environment';

      // 重新啟動相機
      await this.startCamera();

      this.emit('cameraSwitch', {
        facingMode: this.currentFacingMode
      });

      return this.currentFacingMode;
    } catch (error) {
      console.error('攝像頭切換失敗:', error);
      this.emit('cameraSwitchError', error);
      throw error;
    }
  }

  /**
   * 檢查是否支持攝像頭切換
   */
  supportsCameraSwitch() {
    return this.supportsFacingMode && this.availableCameras.length > 1;
  }

  /**
   * 獲取當前攝像頭模式
   */
  getCurrentFacingMode() {
    return this.currentFacingMode;
  }
}

/**
 * 創建相機策略
 */
function createCameraStrategy(mode) {
  switch (mode) {
    case 'web':
      return new WebCameraStrategy();
    case 'mobile':
      return new MobileCameraStrategy();
    default:
      throw new Error(`Unknown camera mode: ${mode}`);
  }
}

// 導出到全局
window.BaseCameraStrategy = BaseCameraStrategy;
window.WebCameraStrategy = WebCameraStrategy;
window.MobileCameraStrategy = MobileCameraStrategy;
window.createCameraStrategy = createCameraStrategy;

.App {
  text-align: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  box-sizing: border-box;
}

.scan-container {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  margin: 20px auto;
  max-width: 500px;
}

.camera-container {
  position: relative;
  width: 100%;
  height: 300px;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  margin: 16px 0;
}

.preview-image {
  max-width: 100%;
  max-height: 280px;
  border-radius: 8px;
  object-fit: contain;
}

.upload-area {
  border: 2px dashed #1890ff;
  border-radius: 8px;
  padding: 40px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f8f9ff;
}

.upload-area:hover {
  border-color: #40a9ff;
  background: #f0f7ff;
}

.upload-text {
  color: #666;
  font-size: 16px;
  margin-top: 8px;
}

.form-section {
  margin: 24px 0;
  text-align: left;
}

.form-section h3 {
  margin-bottom: 16px;
  color: #333;
  font-size: 18px;
}

.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  padding: 16px;
}

.card-item {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.card-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

@media (max-width: 768px) {
  .App {
    padding: 10px;
  }
  
  .scan-container {
    margin: 10px auto;
    padding: 16px;
  }
  
  .card-grid {
    grid-template-columns: 1fr;
    padding: 8px;
  }
} 
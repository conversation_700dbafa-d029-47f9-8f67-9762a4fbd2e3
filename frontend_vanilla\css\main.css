/* 主樣式文件 - 合併原有的 App.css 和 index.css */

/* CSS 變量定義 */
:root {
  --primary-color: #1677ff;
  --primary-hover: #4096ff;
  --primary-active: #0958d9;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #ff4d4f;
  --text-color: #000000d9;
  --text-color-secondary: #00000073;
  --text-color-disabled: #00000040;
  --border-color: #d9d9d9;
  --background-color: #ffffff;
  --background-light: #fafafa;
  --background-grey: #f5f5f5;
  --shadow-light: 0 2px 8px #eee;
  --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.1);
  --shadow-heavy: 0 6px 16px rgba(0, 0, 0, 0.4);
  --border-radius: 8px;
  --border-radius-small: 4px;
  --border-radius-large: 12px;
  --font-size-small: 12px;
  --font-size-base: 14px;
  --font-size-large: 16px;
  --font-size-xl: 18px;
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
}

/* 全局重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--background-grey);
  color: var(--text-color);
  font-size: var(--font-size-base);
  line-height: 1.5;
}

#app {
  min-height: 100vh;
  position: relative;
}

/* 主應用容器 */
.App {
  text-align: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  box-sizing: border-box;
}

/* 加載指示器 */
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--background-color);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-color);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-md);
}

.loading-text {
  color: var(--text-color-secondary);
  font-size: var(--font-size-base);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 掃描容器 */
.scan-container {
  background: var(--background-color);
  border-radius: var(--border-radius-large);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-medium);
  margin: 20px auto;
  max-width: 500px;
}

/* 相機容器 */
.camera-container {
  position: relative;
  width: 100%;
  height: 300px;
  border: 2px dashed var(--border-color);
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--background-light);
  margin: var(--spacing-md) 0;
}

/* 預覽圖片 */
.preview-image {
  max-width: 100%;
  max-height: 280px;
  border-radius: var(--border-radius);
  object-fit: contain;
}

/* 上傳區域 */
.upload-area {
  border: 2px dashed var(--primary-color);
  border-radius: var(--border-radius);
  padding: 40px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f8f9ff;
}

.upload-area:hover {
  border-color: var(--primary-hover);
  background: #f0f7ff;
}

.upload-text {
  color: var(--text-color-secondary);
  font-size: var(--font-size-large);
  margin-top: var(--spacing-sm);
}

/* 表單區域 */
.form-section {
  margin: var(--spacing-lg) 0;
  text-align: left;
}

.form-section h3 {
  margin-bottom: var(--spacing-md);
  color: var(--text-color);
  font-size: var(--font-size-xl);
}

/* 卡片網格 */
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-md);
  padding: var(--spacing-md);
}

.card-item {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
  transition: all 0.3s ease;
  background: var(--background-color);
}

.card-item:hover {
  box-shadow: var(--shadow-medium);
  transform: translateY(-2px);
}

/* 內容區域 */
.content {
  padding: var(--spacing-md);
}

/* 全局滾動條樣式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .App {
    padding: 10px;
  }
  
  .scan-container {
    margin: 10px auto;
    padding: var(--spacing-md);
  }
  
  .card-grid {
    grid-template-columns: 1fr;
    padding: var(--spacing-sm);
  }
  
  body {
    font-size: var(--font-size-large);
    line-height: 1.5;
  }
}

/* 移動端優化 */
@media (max-width: 480px) {
  .App {
    padding: var(--spacing-sm);
  }
  
  .scan-container {
    padding: var(--spacing-md);
    margin: var(--spacing-sm) auto;
  }
  
  .camera-container {
    height: 250px;
  }
}

/* 工具類 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-xs { margin-bottom: var(--spacing-xs); }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }

.mt-xs { margin-top: var(--spacing-xs); }
.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }

.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }

.w-full { width: 100%; }
.h-full { height: 100%; }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

.hidden { display: none; }
.visible { display: block; }

/* 動畫效果 */
.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.fade-out {
  animation: fadeOut 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeOut {
  from { opacity: 1; transform: translateY(0); }
  to { opacity: 0; transform: translateY(-10px); }
}

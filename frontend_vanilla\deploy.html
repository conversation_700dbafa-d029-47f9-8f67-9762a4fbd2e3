<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>部署檢查 - 名片 OCR 應用</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .check-item {
            display: flex;
            align-items: center;
            padding: 12px;
            margin: 8px 0;
            border-radius: 8px;
            border-left: 4px solid #ddd;
        }
        .check-item.success {
            background: #f6ffed;
            border-left-color: #52c41a;
        }
        .check-item.error {
            background: #fff2f0;
            border-left-color: #ff4d4f;
        }
        .check-item.warning {
            background: #fffbe6;
            border-left-color: #faad14;
        }
        .icon {
            margin-right: 12px;
            font-size: 18px;
        }
        .success .icon { color: #52c41a; }
        .error .icon { color: #ff4d4f; }
        .warning .icon { color: #faad14; }
        .btn {
            background: #1677ff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 8px;
        }
        .btn:hover {
            background: #4096ff;
        }
        .btn.success {
            background: #52c41a;
        }
        .btn.success:hover {
            background: #73d13d;
        }
        .section {
            margin: 24px 0;
            padding: 20px;
            border: 1px solid #e8e8e8;
            border-radius: 8px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        pre {
            background: #f8f8f8;
            padding: 12px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 14px;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin: 20px 0;
        }
        .stat-card {
            background: #fafafa;
            padding: 16px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #1677ff;
        }
        .stat-label {
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 名片 OCR 應用 - 部署檢查</h1>
        
        <div class="section">
            <h3>📊 系統狀態</h3>
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number" id="fileCount">-</div>
                    <div class="stat-label">文件數量</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="checksPassed">-</div>
                    <div class="stat-label">檢查通過</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="checksTotal">-</div>
                    <div class="stat-label">總檢查項</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="deployStatus">-</div>
                    <div class="stat-label">部署狀態</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h3>🔍 部署檢查結果</h3>
            <div id="checkResults">
                <div class="check-item">
                    <span class="icon">⏳</span>
                    <span>正在執行檢查...</span>
                </div>
            </div>
        </div>

        <div class="section">
            <h3>🛠️ 操作面板</h3>
            <button class="btn" onclick="runChecks()">🔄 重新檢查</button>
            <button class="btn success" onclick="openApp()" id="openAppBtn" disabled>🚀 打開應用</button>
            <button class="btn" onclick="clearCache()">🗑️ 清除緩存</button>
            <button class="btn" onclick="downloadReport()">📄 下載報告</button>
        </div>

        <div class="section">
            <h3>📋 部署清單</h3>
            <pre id="deploymentChecklist">
✅ 核心文件檢查
✅ 樣式文件檢查  
✅ JavaScript文件檢查
✅ PWA配置檢查
✅ 相機功能檢查
✅ API連接檢查
✅ 瀏覽器兼容性檢查
            </pre>
        </div>

        <div class="section">
            <h3>🌐 環境信息</h3>
            <div id="environmentInfo">
                <p><strong>用戶代理：</strong><span id="userAgent"></span></p>
                <p><strong>屏幕尺寸：</strong><span id="screenSize"></span></p>
                <p><strong>設備類型：</strong><span id="deviceType"></span></p>
                <p><strong>協議：</strong><span id="protocol"></span></p>
                <p><strong>主機：</strong><span id="hostname"></span></p>
            </div>
        </div>
    </div>

    <script>
        let checkResults = [];
        let totalChecks = 0;
        let passedChecks = 0;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateEnvironmentInfo();
            runChecks();
        });

        // 更新環境信息
        function updateEnvironmentInfo() {
            document.getElementById('userAgent').textContent = navigator.userAgent;
            document.getElementById('screenSize').textContent = `${window.innerWidth}x${window.innerHeight}`;
            document.getElementById('deviceType').textContent = /Mobi|Android/i.test(navigator.userAgent) ? '移動設備' : '桌面設備';
            document.getElementById('protocol').textContent = location.protocol;
            document.getElementById('hostname').textContent = location.hostname;
        }

        // 執行檢查
        async function runChecks() {
            checkResults = [];
            totalChecks = 0;
            passedChecks = 0;

            const resultsContainer = document.getElementById('checkResults');
            resultsContainer.innerHTML = '<div class="check-item"><span class="icon">⏳</span><span>正在執行檢查...</span></div>';

            // 檢查項目列表
            const checks = [
                { name: 'HTML文件', test: () => checkFile('index.html') },
                { name: 'CSS樣式文件', test: () => checkFile('css/main.css') },
                { name: 'UI組件樣式', test: () => checkFile('css/components.css') },
                { name: '相機樣式', test: () => checkFile('css/mobile-camera.css') },
                { name: '主應用腳本', test: () => checkFile('js/app.js') },
                { name: '路由系統', test: () => checkFile('js/router.js') },
                { name: 'UI組件', test: () => checkFile('js/components/ui-components.js') },
                { name: '相機組件', test: () => checkFile('js/components/mobile-camera.js') },
                { name: 'API工具', test: () => checkFile('js/utils/api.js') },
                { name: '設備檢測', test: () => checkFile('js/utils/device-detector.js') },
                { name: '相機管理器', test: () => checkFile('js/utils/camera-manager.js') },
                { name: 'PWA清單', test: () => checkFile('manifest.json') },
                { name: 'Service Worker', test: () => checkFile('sw.js') },
                { name: 'HTTPS協議', test: () => checkHTTPS() },
                { name: '相機權限', test: () => checkCameraPermission() },
                { name: 'Service Worker支持', test: () => checkServiceWorkerSupport() },
                { name: 'localStorage支持', test: () => checkLocalStorageSupport() },
                { name: 'Fetch API支持', test: () => checkFetchSupport() }
            ];

            totalChecks = checks.length;

            // 執行所有檢查
            for (const check of checks) {
                try {
                    const result = await check.test();
                    addCheckResult(check.name, result.success, result.message);
                    if (result.success) passedChecks++;
                } catch (error) {
                    addCheckResult(check.name, false, error.message);
                }
            }

            // 更新統計
            updateStats();
            
            // 更新按鈕狀態
            const openAppBtn = document.getElementById('openAppBtn');
            if (passedChecks >= totalChecks * 0.8) {
                openAppBtn.disabled = false;
                openAppBtn.textContent = '🚀 打開應用';
            } else {
                openAppBtn.disabled = true;
                openAppBtn.textContent = '❌ 部署未完成';
            }
        }

        // 檢查文件是否存在
        async function checkFile(path) {
            try {
                const response = await fetch(path, { method: 'HEAD' });
                if (response.ok) {
                    return { success: true, message: '文件存在且可訪問' };
                } else {
                    return { success: false, message: `HTTP ${response.status}: ${response.statusText}` };
                }
            } catch (error) {
                return { success: false, message: `網絡錯誤: ${error.message}` };
            }
        }

        // 檢查HTTPS
        function checkHTTPS() {
            const isHTTPS = location.protocol === 'https:';
            const isLocalhost = location.hostname === 'localhost' || location.hostname === '127.0.0.1';
            
            if (isHTTPS || isLocalhost) {
                return { success: true, message: isHTTPS ? '使用HTTPS協議' : '本地開發環境' };
            } else {
                return { success: false, message: '生產環境需要HTTPS協議以支持相機和PWA功能' };
            }
        }

        // 檢查相機權限
        async function checkCameraPermission() {
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                return { success: false, message: '瀏覽器不支持相機API' };
            }

            try {
                const stream = await navigator.mediaDevices.getUserMedia({ video: true });
                stream.getTracks().forEach(track => track.stop());
                return { success: true, message: '相機權限可用' };
            } catch (error) {
                if (error.name === 'NotAllowedError') {
                    return { success: false, message: '相機權限被拒絕' };
                } else if (error.name === 'NotFoundError') {
                    return { success: false, message: '未找到相機設備' };
                } else {
                    return { success: false, message: `相機錯誤: ${error.message}` };
                }
            }
        }

        // 檢查Service Worker支持
        function checkServiceWorkerSupport() {
            if ('serviceWorker' in navigator) {
                return { success: true, message: '瀏覽器支持Service Worker' };
            } else {
                return { success: false, message: '瀏覽器不支持Service Worker' };
            }
        }

        // 檢查localStorage支持
        function checkLocalStorageSupport() {
            try {
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                return { success: true, message: 'localStorage可用' };
            } catch (error) {
                return { success: false, message: 'localStorage不可用' };
            }
        }

        // 檢查Fetch API支持
        function checkFetchSupport() {
            if (typeof fetch !== 'undefined') {
                return { success: true, message: 'Fetch API可用' };
            } else {
                return { success: false, message: 'Fetch API不可用' };
            }
        }

        // 添加檢查結果
        function addCheckResult(name, success, message) {
            checkResults.push({ name, success, message });
            
            const resultsContainer = document.getElementById('checkResults');
            const className = success ? 'success' : 'error';
            const icon = success ? '✅' : '❌';
            
            const resultHTML = `
                <div class="check-item ${className}">
                    <span class="icon">${icon}</span>
                    <div>
                        <strong>${name}</strong><br>
                        <small>${message}</small>
                    </div>
                </div>
            `;
            
            if (resultsContainer.innerHTML.includes('正在執行檢查')) {
                resultsContainer.innerHTML = resultHTML;
            } else {
                resultsContainer.innerHTML += resultHTML;
            }
        }

        // 更新統計
        function updateStats() {
            document.getElementById('fileCount').textContent = totalChecks;
            document.getElementById('checksPassed').textContent = passedChecks;
            document.getElementById('checksTotal').textContent = totalChecks;
            
            const successRate = (passedChecks / totalChecks * 100).toFixed(0);
            document.getElementById('deployStatus').textContent = `${successRate}%`;
            
            // 更新狀態顏色
            const statusElement = document.getElementById('deployStatus');
            if (successRate >= 90) {
                statusElement.style.color = '#52c41a';
            } else if (successRate >= 70) {
                statusElement.style.color = '#faad14';
            } else {
                statusElement.style.color = '#ff4d4f';
            }
        }

        // 打開應用
        function openApp() {
            window.open('/', '_blank');
        }

        // 清除緩存
        async function clearCache() {
            if ('caches' in window) {
                const cacheNames = await caches.keys();
                await Promise.all(cacheNames.map(name => caches.delete(name)));
                alert('緩存已清除');
            } else {
                alert('瀏覽器不支持緩存API');
            }
        }

        // 下載報告
        function downloadReport() {
            const report = {
                timestamp: new Date().toISOString(),
                environment: {
                    userAgent: navigator.userAgent,
                    screenSize: `${window.innerWidth}x${window.innerHeight}`,
                    protocol: location.protocol,
                    hostname: location.hostname
                },
                results: checkResults,
                summary: {
                    total: totalChecks,
                    passed: passedChecks,
                    successRate: (passedChecks / totalChecks * 100).toFixed(2) + '%'
                }
            };
            
            const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `deployment-report-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>

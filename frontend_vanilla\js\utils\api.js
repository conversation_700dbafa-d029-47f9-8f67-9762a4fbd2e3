/**
 * API調用工具 - 原生JavaScript版本
 * 提供統一的API調用接口
 */

// API基礎配置
const API_BASE_URL = '/api/v1';

/**
 * HTTP請求工具類
 */
class ApiClient {
  constructor(baseURL = API_BASE_URL) {
    this.baseURL = baseURL;
    this.defaultHeaders = {
      'Content-Type': 'application/json'
    };
  }

  /**
   * 發送HTTP請求
   */
  async request(url, options = {}) {
    const fullUrl = url.startsWith('http') ? url : `${this.baseURL}${url}`;
    
    const config = {
      headers: {
        ...this.defaultHeaders,
        ...options.headers
      },
      ...options
    };

    try {
      const response = await fetch(fullUrl, config);
      
      // 檢查響應狀態
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const error = new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
        error.status = response.status;
        error.data = errorData;
        throw error;
      }

      // 處理不同的響應類型
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        return await response.json();
      } else {
        return await response.text();
      }
    } catch (error) {
      console.error('API請求失敗:', error);
      throw error;
    }
  }

  /**
   * GET請求
   */
  async get(url, params = {}) {
    const searchParams = new URLSearchParams(params);
    const queryString = searchParams.toString();
    const fullUrl = queryString ? `${url}?${queryString}` : url;
    
    return this.request(fullUrl, {
      method: 'GET'
    });
  }

  /**
   * POST請求
   */
  async post(url, data = {}, options = {}) {
    const config = {
      method: 'POST',
      ...options
    };

    // 處理不同類型的數據
    if (data instanceof FormData) {
      // FormData不需要設置Content-Type，瀏覽器會自動設置
      delete config.headers;
      config.body = data;
    } else if (typeof data === 'object') {
      config.body = JSON.stringify(data);
    } else {
      config.body = data;
    }

    return this.request(url, config);
  }

  /**
   * PUT請求
   */
  async put(url, data = {}, options = {}) {
    return this.post(url, data, { ...options, method: 'PUT' });
  }

  /**
   * DELETE請求
   */
  async delete(url, options = {}) {
    return this.request(url, {
      method: 'DELETE',
      ...options
    });
  }
}

// 創建API客戶端實例
const apiClient = new ApiClient();

/**
 * 名片相關API
 */
const cardsApi = {
  /**
   * 獲取所有名片
   */
  async getAll(params = {}) {
    return apiClient.get('/cards/', params);
  },

  /**
   * 根據ID獲取名片
   */
  async getById(id) {
    return apiClient.get(`/cards/${id}`);
  },

  /**
   * 創建新名片
   */
  async create(cardData) {
    const formData = new FormData();
    
    // 添加名片數據
    Object.keys(cardData).forEach(key => {
      if (cardData[key] !== null && cardData[key] !== undefined && cardData[key] !== '') {
        formData.append(key, cardData[key]);
      }
    });

    return apiClient.post('/cards/', formData);
  },

  /**
   * 更新名片
   */
  async update(id, cardData) {
    const formData = new FormData();
    
    // 添加名片數據
    Object.keys(cardData).forEach(key => {
      if (cardData[key] !== null && cardData[key] !== undefined && cardData[key] !== '') {
        formData.append(key, cardData[key]);
      }
    });

    return apiClient.put(`/cards/${id}`, formData);
  },

  /**
   * 刪除名片
   */
  async delete(id) {
    return apiClient.delete(`/cards/${id}`);
  },

  /**
   * 搜索名片
   */
  async search(query) {
    return apiClient.get('/cards/search', { q: query });
  }
};

/**
 * OCR相關API
 */
const ocrApi = {
  /**
   * 執行OCR識別
   */
  async recognize(imageFile, options = {}) {
    const formData = new FormData();
    formData.append('file', imageFile);
    
    // 添加其他選項
    Object.keys(options).forEach(key => {
      if (options[key] !== null && options[key] !== undefined) {
        formData.append(key, options[key]);
      }
    });

    return apiClient.post('/ocr/recognize', formData);
  },

  /**
   * 解析OCR文本為結構化數據
   */
  async parse(ocrText, options = {}) {
    return apiClient.post('/ocr/parse', {
      text: ocrText,
      ...options
    });
  },

  /**
   * 一步式OCR+解析
   */
  async recognizeAndParse(imageFile, options = {}) {
    const formData = new FormData();
    formData.append('file', imageFile);
    
    Object.keys(options).forEach(key => {
      if (options[key] !== null && options[key] !== undefined) {
        formData.append(key, options[key]);
      }
    });

    return apiClient.post('/ocr/recognize-and-parse', formData);
  }
};

/**
 * 文件上傳工具
 */
const uploadUtils = {
  /**
   * 將Blob轉換為File對象
   */
  blobToFile(blob, filename = 'image.jpg') {
    return new File([blob], filename, { type: blob.type });
  },

  /**
   * 將DataURL轉換為Blob
   */
  dataURLToBlob(dataURL) {
    const arr = dataURL.split(',');
    const mime = arr[0].match(/:(.*?);/)[1];
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);
    
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    
    return new Blob([u8arr], { type: mime });
  },

  /**
   * 將DataURL轉換為File
   */
  dataURLToFile(dataURL, filename = 'image.jpg') {
    const blob = this.dataURLToBlob(dataURL);
    return this.blobToFile(blob, filename);
  },

  /**
   * 壓縮圖片
   */
  async compressImage(file, maxWidth = 1920, maxHeight = 1080, quality = 0.9) {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();
      
      img.onload = () => {
        // 計算新尺寸
        let { width, height } = img;
        
        if (width > maxWidth || height > maxHeight) {
          const ratio = Math.min(maxWidth / width, maxHeight / height);
          width *= ratio;
          height *= ratio;
        }
        
        // 設置畫布尺寸
        canvas.width = width;
        canvas.height = height;
        
        // 繪製圖片
        ctx.drawImage(img, 0, 0, width, height);
        
        // 轉換為Blob
        canvas.toBlob(resolve, 'image/jpeg', quality);
      };
      
      img.src = URL.createObjectURL(file);
    });
  }
};

/**
 * 錯誤處理工具
 */
const errorHandler = {
  /**
   * 處理API錯誤
   */
  handleApiError(error) {
    let message = '操作失敗，請重試';
    
    if (error.status) {
      switch (error.status) {
        case 400:
          message = error.data?.detail || '請求參數錯誤';
          break;
        case 401:
          message = '未授權，請重新登錄';
          break;
        case 403:
          message = '權限不足';
          break;
        case 404:
          message = '資源不存在';
          break;
        case 500:
          message = '服務器內部錯誤';
          break;
        default:
          message = error.message || `HTTP ${error.status} 錯誤`;
      }
    } else if (error.message) {
      message = error.message;
    }
    
    return message;
  },

  /**
   * 顯示錯誤提示
   */
  showError(error) {
    const message = this.handleApiError(error);
    if (window.Toast) {
      Toast.error(message);
    } else {
      alert(message);
    }
  }
};

// 導出到全局
window.ApiClient = ApiClient;
window.apiClient = apiClient;
window.cardsApi = cardsApi;
window.ocrApi = ocrApi;
window.uploadUtils = uploadUtils;
window.errorHandler = errorHandler;

// 為了兼容性，也導出原有的axios風格API
window.axios = {
  get: (url, config) => apiClient.get(url, config?.params),
  post: (url, data, config) => apiClient.post(url, data, config),
  put: (url, data, config) => apiClient.put(url, data, config),
  delete: (url, config) => apiClient.delete(url, config)
};

<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI測試頁面</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    
    <!-- 圖標字體 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- 主應用容器 -->
    <div id="app">
        <!-- 首頁UI測試 -->
        <div class="App">
            <div class="home-container">
                <div class="home-card">
                    <div class="home-header">
                        <div class="app-logo">
                            <i class="fas fa-id-card"></i>
                        </div>
                        <h1 class="app-title">名片 OCR 應用</h1>
                        <p class="app-subtitle">智能名片掃描與管理</p>
                    </div>
                    
                    <div class="home-actions">
                        <button class="home-btn home-btn-primary" onclick="testToast('primary')">
                            <div class="btn-icon">
                                <i class="fas fa-camera"></i>
                            </div>
                            <div class="btn-content">
                                <div class="btn-title">開始掃描</div>
                                <div class="btn-subtitle">拍照或上傳名片</div>
                            </div>
                            <div class="btn-arrow">
                                <i class="fas fa-chevron-right"></i>
                            </div>
                        </button>
                        
                        <button class="home-btn home-btn-default" onclick="testToast('default')">
                            <div class="btn-icon">
                                <i class="fas fa-address-book"></i>
                            </div>
                            <div class="btn-content">
                                <div class="btn-title">名片管理</div>
                                <div class="btn-subtitle">查看和編輯名片</div>
                            </div>
                            <div class="btn-arrow">
                                <i class="fas fa-chevron-right"></i>
                            </div>
                        </button>
                        
                        <button class="home-btn home-btn-secondary" onclick="testToast('success')">
                            <div class="btn-icon">
                                <i class="fas fa-plus"></i>
                            </div>
                            <div class="btn-content">
                                <div class="btn-title">手動新增</div>
                                <div class="btn-subtitle">直接輸入名片信息</div>
                            </div>
                            <div class="btn-arrow">
                                <i class="fas fa-chevron-right"></i>
                            </div>
                        </button>
                        
                        <button class="home-btn home-btn-warning" onclick="testToast('warning')">
                            <div class="btn-icon">
                                <i class="fas fa-video"></i>
                            </div>
                            <div class="btn-content">
                                <div class="btn-title">相機測試</div>
                                <div class="btn-subtitle">檢測設備兼容性</div>
                            </div>
                            <div class="btn-arrow">
                                <i class="fas fa-chevron-right"></i>
                            </div>
                        </button>
                    </div>
                    
                    <div class="home-footer">
                        <div class="feature-list">
                            <div class="feature-item">
                                <i class="fas fa-check-circle"></i>
                                <span>OCR文字識別</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-check-circle"></i>
                                <span>智能信息提取</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-check-circle"></i>
                                <span>雲端同步存儲</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast 通知容器 -->
    <div id="toast-container" class="toast-container"></div>

    <!-- Modal 容器 -->
    <div id="modal-container"></div>

    <!-- JavaScript -->
    <script src="js/components/ui-components.js"></script>
    
    <script>
        function testToast(type) {
            const messages = {
                primary: '這是主要按鈕測試',
                default: '這是默認按鈕測試',
                success: '操作成功！',
                warning: '這是警告信息'
            };
            
            const toastType = type === 'primary' ? 'info' : 
                             type === 'default' ? 'info' : type;
            
            Toast.show({
                content: messages[type] || '測試消息',
                type: toastType,
                duration: 3000
            });
        }
        
        // 測試按鈕動畫
        document.addEventListener('DOMContentLoaded', function() {
            const buttons = document.querySelectorAll('.home-btn');
            buttons.forEach((button, index) => {
                button.style.opacity = '0';
                button.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    button.style.transition = 'all 0.4s ease-out';
                    button.style.opacity = '1';
                    button.style.transform = 'translateY(0)';
                }, 100 + index * 100);
            });
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="description" content="智能名片掃描與管理應用，支持OCR文字識別和名片信息管理">
    <meta name="keywords" content="名片,OCR,掃描,管理,文字識別,商務">
    <meta name="author" content="名片OCR應用">

    <!-- PWA相關 -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="名片OCR">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="theme-color" content="#667eea">
    <meta name="msapplication-TileColor" content="#667eea">
    <meta name="msapplication-navbutton-color" content="#667eea">

    <!-- Manifest -->
    <link rel="manifest" href="manifest.json">

    <!-- 圖標 -->
    <link rel="icon" type="image/png" sizes="32x32" href="icons/icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="icons/icon-16x16.png">
    <link rel="apple-touch-icon" sizes="180x180" href="icons/icon-180x180.png">
    <link rel="apple-touch-icon" sizes="152x152" href="icons/icon-152x152.png">
    <link rel="apple-touch-icon" sizes="144x144" href="icons/icon-144x144.png">
    <link rel="apple-touch-icon" sizes="120x120" href="icons/icon-120x120.png">
    <link rel="apple-touch-icon" sizes="114x114" href="icons/icon-114x114.png">
    <link rel="apple-touch-icon" sizes="76x76" href="icons/icon-76x76.png">
    <link rel="apple-touch-icon" sizes="72x72" href="icons/icon-72x72.png">
    <link rel="apple-touch-icon" sizes="60x60" href="icons/icon-60x60.png">
    <link rel="apple-touch-icon" sizes="57x57" href="icons/icon-57x57.png">

    <title>名片 OCR 應用</title>

    <!-- CSS 樣式 -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/mobile-camera.css">

    <!-- 圖標字體 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- 主應用容器 -->
    <div id="app">
        <!-- 加載中指示器 -->
        <div id="loading" class="loading-container">
            <div class="loading-spinner"></div>
            <div class="loading-text">載入中...</div>
        </div>
    </div>

    <!-- Toast 通知容器 -->
    <div id="toast-container" class="toast-container"></div>

    <!-- Modal 容器 -->
    <div id="modal-container" class="modal-container"></div>

    <!-- 相機模態框容器 -->
    <div id="camera-modal-container"></div>

    <!-- JavaScript 庫 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/axios/1.6.2/axios.min.js"></script>
    
    <!-- 應用 JavaScript -->
    <script src="js/utils/device-detector.js"></script>
    <script src="js/utils/camera-strategies.js"></script>
    <script src="js/utils/camera-manager.js"></script>
    <script src="js/utils/api.js"></script>
    <script src="js/components/ui-components.js"></script>
    <script src="js/components/mobile-camera.js"></script>
    <script src="js/router.js"></script>
    <script src="js/pages/home.js"></script>
    <script src="js/pages/scan-upload.js"></script>
    <script src="js/pages/card-manager.js"></script>
    <script src="js/pages/add-card.js"></script>
    <script src="js/pages/card-detail.js"></script>
    <script src="js/pages/camera-test.js"></script>
    <script src="js/app.js"></script>

    <script>
        // 應用初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 隱藏加載指示器
            const loading = document.getElementById('loading');
            if (loading) {
                loading.style.display = 'none';
            }
            
            // 初始化應用
            if (window.App) {
                window.App.init();
            }
        });

        // 防止移動端縮放
        document.addEventListener('touchstart', function(event) {
            if (event.touches.length > 1) {
                event.preventDefault();
            }
        });

        let lastTouchEnd = 0;
        document.addEventListener('touchend', function(event) {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                event.preventDefault();
            }
            lastTouchEnd = now;
        }, false);

        // PWA 支持
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                        console.log('ServiceWorker registration successful');
                    })
                    .catch(function(err) {
                        console.log('ServiceWorker registration failed');
                    });
            });
        }
    </script>
</body>
</html>
